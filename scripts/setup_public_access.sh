#!/bin/bash

# Setup script for public access to Gate Control System
# Run with: chmod +x setup_public_access.sh && ./setup_public_access.sh

echo "🚀 Configurando acceso público para el Sistema de Control de Portón"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if running as root
if [[ $EUID -eq 0 ]]; then
   print_error "No ejecutar como root. Use un usuario normal."
   exit 1
fi

print_status "Verificando dependencias..."

# Install required packages
sudo apt update
sudo apt install -y nginx certbot python3-certbot-nginx ufw

print_status "Configurando firewall..."

# Configure UFW firewall
sudo ufw --force enable
sudo ufw allow ssh
sudo ufw allow 80
sudo ufw allow 443
sudo ufw allow 8080

print_status "Obteniendo IP pública..."

# Get public IP
PUBLIC_IP=$(curl -s ifconfig.me)
LOCAL_IP=$(hostname -I | awk '{print $1}')

print_status "IP Local: $LOCAL_IP"
print_status "IP Pública: $PUBLIC_IP"

# Create nginx configuration
print_status "Configurando Nginx como proxy reverso..."

sudo tee /etc/nginx/sites-available/gate-control > /dev/null <<EOF
server {
    listen 80;
    server_name $PUBLIC_IP;
    
    # Security headers
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    
    # Rate limiting
    limit_req_zone \$binary_remote_addr zone=gate_limit:10m rate=10r/m;
    limit_req zone=gate_limit burst=5 nodelay;
    
    # Proxy to Flask app
    location / {
        proxy_pass http://127.0.0.1:8080;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        
        # Timeouts
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
    
    # Block access to sensitive endpoints from external IPs
    location /api/open-gate {
        # Only allow local network access to critical endpoints
        allow ***********/16;
        allow 10.0.0.0/8;
        allow **********/12;
        deny all;
        
        proxy_pass http://127.0.0.1:8080;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
    }
}
EOF

# Enable the site
sudo ln -sf /etc/nginx/sites-available/gate-control /etc/nginx/sites-enabled/
sudo rm -f /etc/nginx/sites-enabled/default

# Test nginx configuration
sudo nginx -t

if [ $? -eq 0 ]; then
    print_status "Configuración de Nginx válida"
    sudo systemctl restart nginx
    sudo systemctl enable nginx
else
    print_error "Error en configuración de Nginx"
    exit 1
fi

# Create systemd service for the gate system
print_status "Creando servicio systemd..."

sudo tee /etc/systemd/system/gate-control.service > /dev/null <<EOF
[Unit]
Description=Gate Control System
After=network.target

[Service]
Type=simple
User=$USER
WorkingDirectory=$(pwd)
Environment=ENABLE_PUBLIC_ACCESS=true
Environment=FLASK_HOST=127.0.0.1
Environment=FLASK_PORT=8080
ExecStart=/usr/bin/python3 run_system.py
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
EOF

sudo systemctl daemon-reload
sudo systemctl enable gate-control.service

print_status "Configurando variables de entorno..."

# Create environment file
cat > .env <<EOF
# Security settings
GATE_API_KEY=$(openssl rand -hex 32)
FLASK_SECRET_KEY=$(openssl rand -hex 32)

# Network settings
ENABLE_PUBLIC_ACCESS=true
FLASK_HOST=127.0.0.1
FLASK_PORT=8080
DEBUG=false

# Optional: Restrict access to specific IPs (comma-separated)
# ALLOWED_IPS=***********,************
EOF

print_status "Generando token de autenticación..."

# Generate auth token
API_KEY=$(grep GATE_API_KEY .env | cut -d'=' -f2)
AUTH_TOKEN=$(echo -n "$API_KEY" | sha256sum | cut -d' ' -f1)

echo "🔑 Token de autenticación: Bearer $AUTH_TOKEN"
echo "📝 Guarde este token para acceder a funciones críticas"

print_status "Configuración completada!"

echo ""
echo "📋 RESUMEN DE CONFIGURACIÓN:"
echo "=========================="
echo "🌐 Acceso local: http://$LOCAL_IP:8080"
echo "🌍 Acceso público: http://$PUBLIC_IP"
echo "🔑 Token de auth: Bearer $AUTH_TOKEN"
echo ""
echo "📝 PRÓXIMOS PASOS:"
echo "=================="
echo "1. Configurar port forwarding en su router:"
echo "   - Puerto externo: 80 → IP interna: $LOCAL_IP:80"
echo "   - Puerto externo: 443 → IP interna: $LOCAL_IP:443"
echo ""
echo "2. (Opcional) Configurar dominio y SSL:"
echo "   sudo certbot --nginx -d su-dominio.com"
echo ""
echo "3. Iniciar el servicio:"
echo "   sudo systemctl start gate-control"
echo ""
echo "4. Ver logs:"
echo "   sudo journalctl -u gate-control -f"
echo ""

print_warning "IMPORTANTE: El endpoint /api/open-gate solo funciona desde la red local por seguridad"
print_warning "Para acceso remoto completo, configure VPN o autenticación adicional"
