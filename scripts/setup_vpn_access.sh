#!/bin/bash

# Setup WireGuard VPN for secure remote access
# Run with: chmod +x setup_vpn_access.sh && sudo ./setup_vpn_access.sh

echo "🔒 Configurando VPN WireGuard para acceso seguro"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if running as root
if [[ $EUID -ne 0 ]]; then
   print_error "Este script debe ejecutarse como root (sudo)"
   exit 1
fi

print_status "Instalando WireGuard..."

# Install WireGuard
apt update
apt install -y wireguard qrencode

# Enable IP forwarding
echo 'net.ipv4.ip_forward=1' >> /etc/sysctl.conf
sysctl -p

print_status "Generando claves..."

# Generate server keys
cd /etc/wireguard
wg genkey | tee server_private.key | wg pubkey > server_public.key
chmod 600 server_private.key

# Generate client keys
wg genkey | tee client_private.key | wg pubkey > client_public.key
chmod 600 client_private.key

# Get keys
SERVER_PRIVATE_KEY=$(cat server_private.key)
SERVER_PUBLIC_KEY=$(cat server_public.key)
CLIENT_PRIVATE_KEY=$(cat client_private.key)
CLIENT_PUBLIC_KEY=$(cat client_public.key)

# Get server public IP
SERVER_PUBLIC_IP=$(curl -s ifconfig.me)
SERVER_LOCAL_IP=$(hostname -I | awk '{print $1}')

print_status "Configurando servidor WireGuard..."

# Create server configuration
cat > /etc/wireguard/wg0.conf <<EOF
[Interface]
PrivateKey = $SERVER_PRIVATE_KEY
Address = ********/24
ListenPort = 51820
PostUp = iptables -A FORWARD -i %i -j ACCEPT; iptables -A FORWARD -o %i -j ACCEPT; iptables -t nat -A POSTROUTING -o eth0 -j MASQUERADE
PostDown = iptables -D FORWARD -i %i -j ACCEPT; iptables -D FORWARD -o %i -j ACCEPT; iptables -t nat -D POSTROUTING -o eth0 -j MASQUERADE

[Peer]
PublicKey = $CLIENT_PUBLIC_KEY
AllowedIPs = ********/32
EOF

print_status "Configurando cliente WireGuard..."

# Create client configuration
cat > /etc/wireguard/client.conf <<EOF
[Interface]
PrivateKey = $CLIENT_PRIVATE_KEY
Address = ********/24
DNS = *******

[Peer]
PublicKey = $SERVER_PUBLIC_KEY
Endpoint = $SERVER_PUBLIC_IP:51820
AllowedIPs = 10.0.0.0/24, ***********/16
PersistentKeepalive = 25
EOF

# Configure firewall
ufw allow 51820/udp
ufw --force enable

# Start WireGuard
systemctl enable wg-quick@wg0
systemctl start wg-quick@wg0

print_status "Generando código QR para cliente móvil..."

# Generate QR code for mobile clients
qrencode -t ansiutf8 < /etc/wireguard/client.conf

print_status "Configuración VPN completada!"

echo ""
echo "📋 CONFIGURACIÓN VPN:"
echo "===================="
echo "🌍 IP del servidor: $SERVER_PUBLIC_IP"
echo "🏠 IP local: $SERVER_LOCAL_IP"
echo "🔗 Puerto VPN: 51820"
echo ""
echo "📱 CONFIGURACIÓN CLIENTE:"
echo "========================"
echo "Archivo de configuración: /etc/wireguard/client.conf"
echo ""
cat /etc/wireguard/client.conf
echo ""
echo "📝 PRÓXIMOS PASOS:"
echo "=================="
echo "1. Configurar port forwarding en router:"
echo "   Puerto 51820 UDP → $SERVER_LOCAL_IP:51820"
echo ""
echo "2. Importar configuración en cliente WireGuard"
echo "3. Conectar VPN y acceder a: http://********:8080"
echo ""
print_warning "Guarde la configuración del cliente en un lugar seguro"
