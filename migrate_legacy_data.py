#!/usr/bin/env python3
"""
Migration script for VecinoSeguro
Migrates existing JSON data to the new database system
"""

import json
import os
import sys
from datetime import datetime, timedelta
from flask import Flask
from config import config
from models import db, User, Vehicle, UserLocation, AccessLog, init_db
from authorization_service import authorization_service

def create_app():
    """Create Flask app for migration"""
    app = Flask(__name__)
    app.config['SECRET_KEY'] = config.SECRET_KEY
    app.config['SQLALCHEMY_DATABASE_URI'] = config.DATABASE_URL
    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = config.SQLALCHEMY_TRACK_MODIFICATIONS
    
    init_db(app)
    return app

def migrate_authorized_plates(app):
    """Migrate authorized_license_plates.json to database"""
    print("🔄 Migrating authorized license plates...")
    
    if not os.path.exists(config.AUTHORIZED_PLATES_FILE):
        print(f"⚠️ No authorized plates file found at {config.AUTHORIZED_PLATES_FILE}")
        return
    
    with app.app_context():
        try:
            # Load existing plates
            with open(config.AUTHORIZED_PLATES_FILE, 'r') as f:
                plates_data = json.load(f)
            
            print(f"📋 Found {len(plates_data)} plates to migrate")
            
            # Create or get legacy user
            legacy_user = User.query.filter_by(email='<EMAIL>').first()
            if not legacy_user:
                print("👤 Creating legacy user account...")
                legacy_user = User(
                    email='<EMAIL>',
                    first_name='Legacy',
                    last_name='System',
                    is_active=True,
                    email_verified=True,
                    is_admin=False
                )
                legacy_user.set_password('legacy123')  # Should be changed!
                db.session.add(legacy_user)
                db.session.flush()
                
                # Add default location for legacy user
                default_location = UserLocation(
                    user_id=legacy_user.id,
                    location_name='Default Location',
                    location_address='Migrated from JSON file',
                    access_level='resident',
                    is_active=True
                )
                db.session.add(default_location)
                print("📍 Added default location for legacy user")
            
            # Migrate each plate
            migrated_count = 0
            skipped_count = 0
            
            for plate, info in plates_data.items():
                # Check if vehicle already exists
                existing_vehicle = Vehicle.query.filter_by(license_plate=plate.upper()).first()
                if existing_vehicle:
                    print(f"⏭️ Skipping {plate} - already exists in database")
                    skipped_count += 1
                    continue
                
                # Create vehicle entry
                vehicle = Vehicle(
                    user_id=legacy_user.id,
                    license_plate=plate.upper(),
                    make=info.get('vehiculo', 'Unknown').split()[0] if info.get('vehiculo') else 'Unknown',
                    model=info.get('vehiculo', 'Unknown') if info.get('vehiculo') else 'Unknown',
                    year=None,
                    color='Unknown',
                    vehicle_type='car',
                    is_active=True,
                    is_temporary=False,
                    notes=f"Migrated from JSON - Casa: {info.get('casa', 'Unknown')}, Owner: {info.get('name', 'Unknown')}"
                )
                
                db.session.add(vehicle)
                migrated_count += 1
                print(f"✅ Migrated {plate} - {info.get('name', 'Unknown')}")
            
            db.session.commit()
            print(f"🎉 Migration completed! Migrated: {migrated_count}, Skipped: {skipped_count}")
            
            # Backup original file
            backup_file = f"{config.AUTHORIZED_PLATES_FILE}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            os.rename(config.AUTHORIZED_PLATES_FILE, backup_file)
            print(f"💾 Original file backed up to: {backup_file}")
            
        except Exception as e:
            db.session.rollback()
            print(f"❌ Error during migration: {e}")
            raise

def migrate_log_files(app):
    """Migrate existing log files to database"""
    print("🔄 Migrating log files...")
    
    logs_dir = config.LOGS_DIR
    if not os.path.exists(logs_dir):
        print(f"⚠️ No logs directory found at {logs_dir}")
        return
    
    with app.app_context():
        try:
            migrated_count = 0
            
            # Find all JSON log files
            for filename in os.listdir(logs_dir):
                if filename.endswith('.json') and filename.startswith('event_'):
                    filepath = os.path.join(logs_dir, filename)
                    
                    try:
                        with open(filepath, 'r') as f:
                            event_data = json.load(f)
                        
                        # Extract event information
                        timestamp_str = event_data.get('timestamp')
                        if timestamp_str:
                            try:
                                timestamp = datetime.fromisoformat(timestamp_str.replace('Z', '+00:00'))
                            except:
                                timestamp = datetime.utcnow()
                        else:
                            timestamp = datetime.utcnow()
                        
                        license_plate = event_data.get('placa')
                        location = event_data.get('ubicacion', 'unknown')
                        access_granted = event_data.get('autorizado', False)
                        image_path = event_data.get('imagen')
                        notes = f"Migrated from {filename}"
                        
                        # Try to find associated user/vehicle
                        user_id = None
                        vehicle_id = None
                        
                        if license_plate:
                            vehicle = Vehicle.query.filter_by(license_plate=license_plate.upper()).first()
                            if vehicle:
                                vehicle_id = vehicle.id
                                user_id = vehicle.user_id
                        
                        # Check if this log entry already exists
                        existing_log = AccessLog.query.filter_by(
                            license_plate=license_plate.upper() if license_plate else None,
                            location=location,
                            timestamp=timestamp
                        ).first()
                        
                        if existing_log:
                            continue  # Skip duplicates
                        
                        # Create access log entry
                        access_log = AccessLog(
                            user_id=user_id,
                            vehicle_id=vehicle_id,
                            license_plate=license_plate.upper() if license_plate else None,
                            location=location,
                            access_granted=access_granted,
                            access_method='automatic',
                            image_path=image_path,
                            ip_address='system',
                            notes=notes,
                            timestamp=timestamp
                        )
                        
                        db.session.add(access_log)
                        migrated_count += 1
                        
                        if migrated_count % 100 == 0:
                            db.session.commit()
                            print(f"📝 Migrated {migrated_count} log entries...")
                    
                    except Exception as e:
                        print(f"⚠️ Error processing {filename}: {e}")
                        continue
            
            db.session.commit()
            print(f"🎉 Log migration completed! Migrated: {migrated_count} entries")
            
        except Exception as e:
            db.session.rollback()
            print(f"❌ Error during log migration: {e}")
            raise

def verify_migration(app):
    """Verify the migration was successful"""
    print("🔍 Verifying migration...")
    
    with app.app_context():
        try:
            # Count vehicles
            vehicle_count = Vehicle.query.filter_by(is_active=True).count()
            print(f"📊 Active vehicles in database: {vehicle_count}")
            
            # Count users
            user_count = User.query.filter_by(is_active=True).count()
            print(f"👥 Active users in database: {user_count}")
            
            # Count access logs
            log_count = AccessLog.query.count()
            print(f"📝 Access logs in database: {log_count}")
            
            # Test authorization service
            all_plates = authorization_service.get_all_authorized_plates()
            print(f"🔑 Total authorized plates: {len(all_plates)}")
            
            # Show breakdown by source
            db_plates = sum(1 for p in all_plates.values() if p.get('source') == 'database')
            json_plates = sum(1 for p in all_plates.values() if p.get('source') == 'json_file')
            print(f"   - Database: {db_plates}")
            print(f"   - JSON file: {json_plates}")
            
            print("✅ Migration verification completed successfully!")
            
        except Exception as e:
            print(f"❌ Error during verification: {e}")
            raise

def main():
    """Main migration function"""
    print("🚀 Starting VecinoSeguro data migration...")
    print("=" * 50)
    
    app = create_app()
    
    try:
        # Run migrations
        migrate_authorized_plates(app)
        print()
        migrate_log_files(app)
        print()
        verify_migration(app)
        
        print("=" * 50)
        print("🎉 Migration completed successfully!")
        print()
        print("📋 Next steps:")
        print("1. Update the legacy user password: <EMAIL>")
        print("2. Create proper user accounts for vehicle owners")
        print("3. Assign vehicles to the correct users")
        print("4. Test the gate control system")
        print("5. Remove or archive the backup files when satisfied")
        
    except Exception as e:
        print(f"💥 Migration failed: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
