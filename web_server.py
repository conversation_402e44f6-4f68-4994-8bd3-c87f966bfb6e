from flask import Flask, render_template, jsonify, request, send_file, session
from flask_limiter import Limiter
from flask_limiter.util import get_remote_address
from flask_cors import CORS
from flask_login import login_required, current_user
import json
import os
from datetime import datetime, timedelta
from src.utils.config import config
import secrets
import hashlib
from functools import wraps

# Import database and authentication
from src.models.models import db, init_db, User, Vehicle, AccessLog
from src.api.auth import init_auth, jwt_required, admin_required
from api_routes import init_api_routes
from src.security.security_middleware import security_middleware, require_secure_headers, get_security_stats
from src.security.audit_log import AuditLogger
from src.security.authorization_service import authorization_service
from src.security.csrf_protection import CSRFProtection
from src.security.session_security import SecureSessionManager
from src.security.security_headers import SecurityHeaders, security_config

# Importar la función para obtener el relé compartido
from gate_control_system import get_gate_relay

app = Flask(__name__)

# Configure Flask app
app.config['SECRET_KEY'] = config.SECRET_KEY
app.config['SQLALCHEMY_DATABASE_URI'] = config.DATABASE_URL
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = config.SQLALCHEMY_TRACK_MODIFICATIONS
app.config['JWT_SECRET_KEY'] = config.JWT_SECRET_KEY
app.config['WTF_CSRF_ENABLED'] = config.WTF_CSRF_ENABLED
app.config['WTF_CSRF_TIME_LIMIT'] = config.WTF_CSRF_TIME_LIMIT

# Initialize security configuration
security_config.init_app(app)

# Configure session for development
app.config.update({
    'SESSION_COOKIE_SECURE': False,  # Allow HTTP for development
    'SESSION_COOKIE_HTTPONLY': True,
    'SESSION_COOKIE_SAMESITE': 'Lax',  # Less strict for development
    'PERMANENT_SESSION_LIFETIME': timedelta(hours=24)
})

# Initialize database and authentication
init_db(app)
init_auth(app)
init_api_routes(app)

# Initialize enhanced security components
csrf_protection = CSRFProtection(app)
# session_manager = SecureSessionManager(app)  # Temporarily disabled
security_headers = SecurityHeaders(app)

# Apply security middleware to all requests (disabled for development)
# @app.before_request
# def apply_security_middleware():
#     return security_middleware()

# CORS configuration - restrict for production
CORS(app, origins=["http://localhost:3000", "http://127.0.0.1:3000"])

# Very relaxed rate limiting for development and testing
limiter = Limiter(
    key_func=get_remote_address,
    app=app,
    default_limits=["100000 per day", "10000 per hour", "1000 per minute"]
)

# Simple authentication for public access
def require_auth(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        auth_token = request.headers.get('Authorization')
        if not auth_token or not verify_token(auth_token):
            return jsonify({'error': 'Acceso no autorizado'}), 401
        return f(*args, **kwargs)
    return decorated_function

def verify_token(token):
    # Simple token verification - replace with your secure method
    expected_token = hashlib.sha256(config.API_KEY.encode()).hexdigest()
    return token == f"Bearer {expected_token}"

class WebController:
    def __init__(self):
        # Keep backward compatibility with JSON file
        self.authorized_plates = self.load_authorized_plates()
        # Usar instancia compartida del relé (with error handling for development)
        try:
            self.gate_relay = get_gate_relay()
        except Exception as e:
            app.logger.warning(f"GPIO not available (development mode): {e}")
            self.gate_relay = None

    def load_authorized_plates(self):
        """Load authorized plates from JSON file (backward compatibility)"""
        try:
            with open(config.AUTHORIZED_PLATES_FILE, "r") as f:
                return json.load(f)
        except:
            return {}

    def save_authorized_plates(self):
        """Save authorized plates to JSON file (backward compatibility)"""
        with open(config.AUTHORIZED_PLATES_FILE, "w") as f:
            json.dump(self.authorized_plates, f, indent=2)

    def get_all_authorized_plates(self):
        """Get all authorized plates using the unified authorization service"""
        return authorization_service.get_all_authorized_plates()

    def is_plate_authorized(self, license_plate, location=None):
        """Check if a license plate is authorized using the unified authorization service"""
        result = authorization_service.check_plate_authorization(license_plate, location)

        if result.authorized:
            # Return format compatible with existing code
            info = {
                'source': result.source,
                'reason': result.reason
            }

            if result.user_info:
                info['user'] = result.user_info
            if result.vehicle_info:
                info['vehicle'] = result.vehicle_info
            if result.location_access:
                info['location_access'] = result.location_access

            return True, info
        else:
            return False, {
                'reason': result.reason,
                'source': result.source
            }
    
    def get_recent_logs(self, hours=24):
        """Get recent access logs from database and JSON files"""
        try:
            cutoff_time = datetime.utcnow() - timedelta(hours=hours)
            logs = []

            # Get logs from database
            try:
                db_logs = AccessLog.query.filter(
                    AccessLog.timestamp >= cutoff_time
                ).order_by(AccessLog.timestamp.desc()).all()

                for log in db_logs:
                    logs.append({
                        'timestamp': log.timestamp.isoformat(),
                        'ubicacion': log.location,
                        'placa': log.license_plate,
                        'autorizado': log.access_granted,
                        'detalles': log.notes or '',
                        'imagen': log.image_path,
                        'ip_origen': log.ip_address or 'system',
                        'usuario': log.user.full_name if log.user else 'Unknown',
                        'source': 'database'
                    })
            except Exception as e:
                app.logger.error(f"Error loading logs from database: {e}")

            # Also get logs from JSON files (backward compatibility)
            try:
                log_dir = config.LOGS_DIR
                if os.path.exists(log_dir):
                    for filename in os.listdir(log_dir):
                        if filename.startswith("event_") and filename.endswith(".json"):
                            filepath = os.path.join(log_dir, filename)
                            try:
                                with open(filepath, "r") as f:
                                    log_data = json.load(f)
                                    log_time = datetime.fromisoformat(log_data["timestamp"])
                                    if log_time > cutoff_time:
                                        log_data['source'] = 'json_file'
                                        logs.append(log_data)
                            except:
                                continue
            except Exception as e:
                app.logger.error(f"Error loading logs from files: {e}")

            return sorted(logs, key=lambda x: x.get('timestamp', ''), reverse=True)

        except Exception as e:
            app.logger.error(f"Error getting recent logs: {e}")
            return []

    def log_access_attempt(self, license_plate, location, access_granted, user_info=None, image_path=None, ip_address=None, notes=None):
        """Log access attempt to database"""
        try:
            # Find user and vehicle if possible
            user = None
            vehicle = None

            if license_plate:
                vehicle = Vehicle.query.filter_by(
                    license_plate=license_plate.upper(),
                    is_active=True
                ).first()
                if vehicle:
                    user = vehicle.user

            # Use the authorization service for consistent logging
            auth_result = authorization_service.AuthorizationResult(
                authorized=access_granted,
                user_info=user.to_dict() if user else user_info,
                vehicle_info=vehicle.to_dict() if vehicle else None,
                source='web_controller',
                reason=notes or ('Access granted' if access_granted else 'Access denied')
            )

            # Use the authorization service to log the event
            authorization_service.log_access_attempt(
                license_plate=license_plate,
                location=location,
                authorization_result=auth_result,
                image_path=image_path,
                ip_address=ip_address or 'system'
            )

        except Exception as e:
            app.logger.error(f"Error logging access attempt: {e}")
            # Fallback to JSON logging
            self._log_to_json_file(license_plate, location, access_granted, user_info, image_path, ip_address, notes)

    def _log_to_json_file(self, license_plate, location, access_granted, user_info, image_path, ip_address, notes):
        """Fallback JSON file logging"""
        try:
            relative_image_path = None
            if image_path:
                relative_image_path = os.path.relpath(image_path, config.SNAPSHOTS_DIR)

            event = {
                "timestamp": datetime.utcnow().isoformat(),
                "ubicacion": location,
                "placa": license_plate,
                "autorizado": access_granted,
                "detalles": notes or '',
                "imagen": relative_image_path,
                "ip_origen": ip_address or "system"
            }

            filename = f"logs/event_{location}_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}.json"
            os.makedirs(os.path.dirname(filename), exist_ok=True)
            with open(filename, "w") as f:
                json.dump(event, f, indent=2)

            app.logger.info(f"📝 Evento guardado en JSON: {filename}")

        except Exception as e:
            app.logger.error(f"❌ Error guardando evento en JSON: {e}")

controller = WebController()

@app.route('/')
@login_required
def dashboard():
    """Main dashboard - requires authentication"""
    return render_template('dashboard.html', user=current_user)

@app.route('/login')
def login_page():
    """Login page"""
    return render_template('login.html')

@app.route('/register')
def register_page():
    """Registration page"""
    return render_template('register.html')

@app.route('/profile')
@login_required
def profile_page():
    """User profile page"""
    return render_template('profile.html', user=current_user)

@app.route('/test')
def test_page():
    """Test page for debugging - no auth required"""
    return render_template('test.html')

@app.route('/debug/auth')
def debug_auth():
    """Debug authentication status"""
    return jsonify({
        'authenticated': current_user.is_authenticated,
        'user_id': current_user.id if current_user.is_authenticated else None,
        'user_email': current_user.email if current_user.is_authenticated else None,
        'session_data': dict(session)
    })

@app.route('/api/security/stats')
@jwt_required
@admin_required
def get_security_stats_endpoint():
    """Get security statistics (admin only)"""
    try:
        stats = get_security_stats()
        return jsonify({
            'success': True,
            'security_stats': stats
        })
    except Exception as e:
        app.logger.error(f"Error getting security stats: {str(e)}")
        return jsonify({'error': 'Error interno del servidor'}), 500

@app.route('/api/logs')
@jwt_required
@admin_required
def get_logs():
    """Get access logs with user-specific filtering"""
    try:
        user = request.current_user
        hours = request.args.get('hours', 24, type=int)
        limit = request.args.get('limit', 100, type=int)

        # Validate parameters
        if hours < 1 or hours > 168:  # Max 1 week
            hours = 24
        if limit < 1 or limit > 500:  # Max 500 logs
            limit = 100

        logs = controller.get_recent_logs(hours)

        if user.is_admin:
            # Admin sees all logs
            filtered_logs = logs[:limit]
        else:
            # Regular users see only their own logs
            user_plates = [v.license_plate for v in user.vehicles if v.is_active]
            filtered_logs = [
                log for log in logs
                if (log.get('placa') in user_plates or
                    log.get('usuario') == user.full_name)
            ][:limit]

        return jsonify({
            'success': True,
            'logs': filtered_logs,
            'total': len(filtered_logs),
            'hours': hours,
            'user_role': 'admin' if user.is_admin else 'user'
        })

    except Exception as e:
        app.logger.error(f"Error getting logs: {str(e)}")
        return jsonify({'error': 'Error interno del servidor'}), 500

@app.route('/api/plates')
@jwt_required
def get_plates():
    """Get authorized plates for current user"""
    try:
        user = request.current_user

        if user.is_admin:
            # Admin can see all plates
            plates = controller.get_all_authorized_plates()
        else:
            # Regular users see only their plates
            plates = {}
            for vehicle in user.vehicles:
                if vehicle.is_active and not vehicle.is_expired():
                    plates[vehicle.license_plate] = {
                        'name': user.full_name,
                        'make': vehicle.make,
                        'model': vehicle.model,
                        'vehicle_id': vehicle.id,
                        'source': 'database'
                    }

        return jsonify(plates)

    except Exception as e:
        app.logger.error(f"Error getting plates: {str(e)}")
        return jsonify({'error': 'Error interno del servidor'}), 500

@app.route('/api/plates', methods=['POST'])
@jwt_required
@limiter.limit("10 per minute")
def add_plate():
    """Add a new vehicle/plate for the current user"""
    try:
        user = request.current_user
        data = request.json
        if not data:
            return jsonify({'error': 'Datos JSON requeridos'}), 400

        plate = data.get('plate', '').strip().upper()
        make = data.get('make', '').strip()
        model = data.get('model', '').strip()
        year = data.get('year')
        color = data.get('color', '').strip()
        vehicle_type = data.get('vehicle_type', 'car').strip()
        is_temporary = data.get('is_temporary', False)

        # Validate plate format using proper validation
        try:
            from src.api.validation import InputValidator
            plate = InputValidator.validate_license_plate(plate)
        except Exception as e:
            return jsonify({'error': str(e)}), 400

        # Check if plate already exists
        existing_vehicle = Vehicle.query.filter_by(license_plate=plate).first()
        if existing_vehicle:
            return jsonify({'error': 'Esta placa ya está registrada'}), 400

        # Create new vehicle
        vehicle = Vehicle(
            user_id=user.id,
            license_plate=plate,
            make=make or 'Unknown',
            model=model or 'Unknown',
            year=year if year and str(year).isdigit() else None,
            color=color or 'Unknown',
            vehicle_type=vehicle_type,
            is_active=True,
            is_temporary=is_temporary
        )

        # Set expiration for temporary vehicles (30 days)
        if is_temporary:
            vehicle.expires_at = datetime.utcnow() + timedelta(days=30)

        db.session.add(vehicle)
        db.session.commit()

        app.logger.info(f"New vehicle added: {plate} by {user.email}")
        return jsonify({
            'success': True,
            'vehicle': vehicle.to_dict(),
            'message': 'Vehículo agregado exitosamente'
        })

    except Exception as e:
        db.session.rollback()
        app.logger.error(f"Error adding vehicle: {str(e)}")
        return jsonify({'error': 'Error interno del servidor'}), 500

@app.route('/api/plates/<plate>', methods=['DELETE'])
@jwt_required
@limiter.limit("5 per minute")
def delete_plate(plate):
    """Delete a license plate (admin only or own vehicles)"""
    try:
        user = request.current_user
        plate = plate.upper()

        # Check if it's a vehicle in the database
        vehicle = Vehicle.query.filter_by(license_plate=plate, is_active=True).first()

        if vehicle:
            # Users can only delete their own vehicles, admins can delete any
            if not user.is_admin and vehicle.user_id != user.id:
                return jsonify({'error': 'No tienes permiso para eliminar este vehículo'}), 403

            # Soft delete the vehicle
            vehicle.is_active = False
            db.session.commit()

            app.logger.info(f"Vehicle {plate} deactivated by {user.email}")
            return jsonify({'success': True, 'message': 'Vehículo eliminado exitosamente'})

        # Fallback to JSON file (admin only)
        if not user.is_admin:
            return jsonify({'error': 'No tienes permiso para realizar esta acción'}), 403

        if plate in controller.authorized_plates:
            del controller.authorized_plates[plate]
            controller.save_authorized_plates()
            app.logger.info(f"JSON plate {plate} deleted by admin {user.email}")
            return jsonify({'success': True, 'message': 'Placa eliminada exitosamente'})

        return jsonify({'error': 'Placa no encontrada'}), 404

    except Exception as e:
        db.session.rollback()
        app.logger.error(f"Error deleting plate {plate}: {str(e)}")
        return jsonify({'error': 'Error interno del servidor'}), 500

@app.route('/api/stats')
@jwt_required
@admin_required
def get_stats():
    """Get system statistics with user-specific filtering"""
    try:
        user = request.current_user
        logs = controller.get_recent_logs(24)

        if user.is_admin:
            # Admin sees all stats
            stats = {
                'total_accesos': len(logs),
                'autorizados': len([l for l in logs if l.get('autorizado')]),
                'no_autorizados': len([l for l in logs if not l.get('autorizado')]),
                'entradas': len([l for l in logs if l.get('ubicacion') == 'entrada']),
                'salidas': len([l for l in logs if l.get('ubicacion') == 'salida']),
                'total_placas': len(controller.get_all_authorized_plates()),
                'user_role': 'admin'
            }
        else:
            # Regular users see only their own stats
            user_logs = [l for l in logs if l.get('usuario') == user.full_name or
                        any(v.license_plate == l.get('placa') for v in user.vehicles if v.is_active)]

            stats = {
                'total_accesos': len(user_logs),
                'autorizados': len([l for l in user_logs if l.get('autorizado')]),
                'no_autorizados': len([l for l in user_logs if not l.get('autorizado')]),
                'entradas': len([l for l in user_logs if l.get('ubicacion') == 'entrada']),
                'salidas': len([l for l in user_logs if l.get('ubicacion') == 'salida']),
                'total_placas': len([v for v in user.vehicles if v.is_active]),
                'user_role': 'user'
            }

        return jsonify(stats)

    except Exception as e:
        app.logger.error(f"Error getting stats: {str(e)}")
        return jsonify({'error': 'Error interno del servidor'}), 500

@app.route('/api/open-gate', methods=['POST'])
@jwt_required
@limiter.limit("3 per minute")  # Stricter limit for public access
def manual_open_gate():
    """Manually open gate (requires authentication)"""
    try:
        user = request.current_user
        data = request.json or {}
        duration = data.get('duration', 10)

        if not isinstance(duration, int) or duration < 1 or duration > 30:
            return jsonify({'error': 'Duración debe ser entre 1 y 30 segundos'}), 400

        # Log the manual operation attempt
        client_ip = get_remote_address()
        app.logger.warning(f"Manual gate operation requested by {user.full_name} from IP: {client_ip}, duration: {duration}s")

        if not controller.gate_relay:
            app.logger.warning("Gate relay not available (development mode)")
            # Still log the attempt
            controller.log_access_attempt(
                license_plate=None,
                location='manual',
                access_granted=True,
                user_info={'user_id': user.id, 'user_name': user.full_name},
                ip_address=client_ip,
                notes=f'Manual gate opening by {user.full_name} (development mode)'
            )
            return jsonify({'success': True, 'message': 'Portón abierto (modo desarrollo)'})

        controller.gate_relay.manual_open(duration)

        # Registrar evento manual
        event = {
            "timestamp": datetime.now().isoformat(),
            "ubicacion": "manual",
            "placa": "APERTURA_MANUAL",
            "autorizado": True,
            "imagen": None,
            "duracion": duration,
            "detalles": "Apertura manual",
            "ip_origen": client_ip
        }

        filename = f"logs/event_manual_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        os.makedirs("logs", exist_ok=True)
        with open(filename, "w") as f:
            json.dump(event, f, indent=2)

        return jsonify({'success': True, 'message': f'Portón abierto por {duration} segundos'})

    except Exception as e:
        app.logger.error(f"Error in manual gate operation: {str(e)}")
        return jsonify({'error': f'Error abriendo portón: {str(e)}'}), 500

@app.route('/api/health')
def health_check():
    """Health check endpoint for monitoring"""
    try:
        # Check if relay is accessible
        relay_status = "ok" if controller.gate_relay else "error"

        # Check if logs directory exists
        logs_status = "ok" if os.path.exists(config.LOGS_DIR) else "error"

        # Check if authorized plates file exists
        plates_status = "ok" if os.path.exists(config.AUTHORIZED_PLATES_FILE) else "warning"

        health_data = {
            "status": "healthy" if all(s == "ok" for s in [relay_status, logs_status]) else "degraded",
            "timestamp": datetime.now().isoformat(),
            "components": {
                "relay": relay_status,
                "logs": logs_status,
                "plates_file": plates_status
            },
            "version": "1.0.0"
        }

        status_code = 200 if health_data["status"] == "healthy" else 503
        return jsonify(health_data), status_code

    except Exception as e:
        return jsonify({
            "status": "unhealthy",
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }), 503

@app.route('/images/<path:filename>')
def serve_image(filename):
    try:
        # Validate filename to prevent directory traversal
        if '..' in filename or filename.startswith('/'):
            return jsonify({'error': 'Nombre de archivo inválido'}), 400

        # Construir ruta completa de la imagen
        image_path = os.path.join(config.SNAPSHOTS_DIR, filename)

        # Verificar que el archivo existe y está dentro del directorio permitido
        if not os.path.exists(image_path) or not os.path.commonpath([config.SNAPSHOTS_DIR, image_path]) == config.SNAPSHOTS_DIR:
            return jsonify({'error': 'Imagen no encontrada'}), 404

        # Servir la imagen
        return send_file(image_path)
    except Exception as e:
        app.logger.error(f"Error serving image {filename}: {str(e)}")
        return jsonify({'error': 'Error sirviendo imagen'}), 500

# Error handlers
@app.errorhandler(404)
def not_found(error):
    return jsonify({'error': 'Endpoint no encontrado'}), 404

@app.errorhandler(405)
def method_not_allowed(error):
    return jsonify({'error': 'Método no permitido'}), 405

@app.errorhandler(429)
def ratelimit_handler(e):
    return jsonify({'error': 'Demasiadas solicitudes. Intente más tarde.'}), 429

@app.errorhandler(500)
def internal_error(error):
    app.logger.error(f"Internal server error: {str(error)}")
    return jsonify({'error': 'Error interno del servidor'}), 500

if __name__ == '__main__':
    # Set development mode for more permissive CSP
    app.config['ENV'] = 'development'
    app.run(host='0.0.0.0', port=8082, debug=True)
