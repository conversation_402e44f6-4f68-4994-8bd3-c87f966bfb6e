import time
import json
import os
from src.utils.config import config
from datetime import datetime
from src.utils.net import online
from src.utils.logger import setup_logger
from hardware.camera import Camera
from hardware.relay_module import RelayModule
from hardware.distance_sensor import DistanceSensor
from hardware.reed_switch import ReedSwitch
from src.security.authorization_service import authorization_service

_gate_relay_instance = None

def get_gate_relay():
    global _gate_relay_instance
    if _gate_relay_instance is None:
        try:
            _gate_relay_instance = RelayModule(pin=config.RELAY_PIN)
        except Exception as e:
            print(f"Warning: GPIO not available (development mode): {e}")
            _gate_relay_instance = None
    return _gate_relay_instance

class GateControlSystem:
    
    def __init__(self):
        self.logger = setup_logger("gate_system")
        self.logger.info("🚀 Iniciando sistema de control de portón...")

        self.camera_entrada = Camera(config.RTSP_CAMERA_ENTRADA, "entrada")
        self.camera_salida = Camera(config.RTSP_CAMERA_SALIDA, "salida")

        self.sensor_entrada = DistanceSensor(
            trigger_pin=config.PIN_SENSOR_ENTRADA_TRIGGER,
            echo_pin=config.PIN_SENSOR_ENTRADA_ECHO,
            threshold_distance=1.5
        )
        self.sensor_salida = DistanceSensor(
            trigger_pin=config.PIN_SENSOR_SALIDA_TRIGGER,
            echo_pin=config.PIN_SENSOR_SALIDA_ECHO,
            threshold_distance=1.5
        )
        self.sensor_pasada = DistanceSensor(
            trigger_pin=config.PIN_SENSOR_PASADA_TRIGGER,
            echo_pin=config.PIN_SENSOR_PASADA_ECHO,
            threshold_distance=2.0
        )

        self.gate_state = ReedSwitch(pin=18, active_high=False)
        
        # self.gate_state.add_state_change_callback(self._on_gate_state_change)

        self.gate_relay = get_gate_relay()
        # self.gate_is_open = False
        
        self.create_directories()
        
        self.logger.info("✅ Sistema inicializado correctamente")
    
    
    def create_directories(self):
        os.makedirs(os.path.join(config.SNAPSHOTS_DIR, "entrada"), exist_ok=True)
        os.makedirs(os.path.join(config.SNAPSHOTS_DIR, "salida"), exist_ok=True)
        os.makedirs("logs", exist_ok=True)
    
    
    def is_plate_authorized(self, plate, location=None):
        """Check if plate is authorized using the unified authorization service"""
        try:
            result = authorization_service.check_plate_authorization(plate, location)

            if result.authorized:
                # Return format compatible with existing code
                info = {
                    'name': result.user_info.get('name', 'Unknown') if result.user_info else 'Unknown',
                    'source': result.source,
                    'reason': result.reason
                }

                # Add vehicle info if available
                if result.vehicle_info:
                    info.update({
                        'make': result.vehicle_info.get('make', 'Unknown'),
                        'model': result.vehicle_info.get('model', 'Unknown'),
                        'vehicle_id': result.vehicle_info.get('id')
                    })

                # Add user info if available
                if result.user_info:
                    info.update({
                        'user_id': result.user_info.get('id'),
                        'full_name': result.user_info.get('full_name', result.user_info.get('name', 'Unknown'))
                    })

                return True, info
            else:
                self.logger.warning(f"⚠️ Placa {plate} no autorizada: {result.reason}")
                return False, {'reason': result.reason, 'source': result.source}

        except Exception as e:
            self.logger.error(f"❌ Error verificando placa: {e}")
            return False, {'reason': f'Error: {str(e)}', 'source': 'error'}
    
    
    def save_event_log(self, plate, authorized, info, location, image_path):
        """Save event log using the unified authorization service (backward compatibility)"""
        try:
            # Create authorization result from the provided info
            auth_result = authorization_service.AuthorizationResult(
                authorized=authorized,
                user_info=info if isinstance(info, dict) else None,
                source='gate_control',
                reason=str(info) if info else 'Gate control event'
            )

            # Use the authorization service to log the event
            authorization_service.log_access_attempt(
                license_plate=plate,
                location=location,
                authorization_result=auth_result,
                image_path=image_path,
                ip_address='gate_system'
            )

            self.logger.info(f"📝 Event logged via authorization service: {plate} at {location}")

        except Exception as e:
            self.logger.error(f"❌ Error logging event: {e}")
            # Fallback to original JSON logging
            self._save_event_to_json_fallback(plate, authorized, info, location, image_path)

    def _save_event_to_json_fallback(self, plate, authorized, info, location, image_path):
        """Fallback JSON logging for gate control system"""
        try:
            relative_image_path = None
            if image_path:
                relative_image_path = os.path.relpath(image_path, config.SNAPSHOTS_DIR)

            event = {
                "timestamp": datetime.now().isoformat(),
                "ubicacion": location,
                "placa": plate,
                "autorizado": authorized,
                "detalles": str(info) if info else 'No info',
                "imagen": relative_image_path,
                "ip_origen": "gate_system",
                "source": "gate_control_fallback"
            }

            os.makedirs("logs", exist_ok=True)
            filename = f"logs/gate_event_{location}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            with open(filename, "w") as f:
                json.dump(event, f, indent=2)

            self.logger.info(f"📝 Fallback event saved: {filename}")

        except Exception as e:
            self.logger.error(f"❌ Error in fallback logging: {e}")
    
    
    # def _on_gate_state_change(self, is_closed):
    #     if is_closed:
    #         self.gate_is_open = False
    #         self.logger.info("🔒 Portón detectado como cerrado")
    #     else:
    #         self.gate_is_open = True
    #         self.logger.info("🔓 Portón detectado como abierto")
    
    
    def open_gate(self):
        self.logger.info("🔒 Abriendo portón...")
        try:
            self.gate_relay.pulse()
        except Exception as e:
            self.logger.error(f"❌ Error abriendo portón: {e}")
    
    
    def close_gate(self):        
        self.logger.info("🔒 Cerrando portón...")
        try:
            time.sleep(3)
            self.gate_relay.pulse()
        except Exception as e:
            self.logger.error(f"❌ Error cerrando portón: {e}")

    
    def is_authorized_vehicle(self, location):
        """Check if vehicle at location is authorized with enhanced logging"""
        # Take snapshot
        camera = self.camera_entrada if location == "entrada" else self.camera_salida
        image_path = camera.take_snapshot(location)

        if not image_path:
            self.logger.warning(f"⚠️ No se pudo capturar imagen en {location}")
            # Log failed image capture
            authorization_service.log_access_attempt(
                license_plate=None,
                location=location,
                authorization_result=authorization_service.AuthorizationResult(
                    authorized=False,
                    reason="Failed to capture image",
                    source="camera_error"
                ),
                image_path=None
            )
            time.sleep(config.CYCLE_INTERVAL)
            return False

        # Check internet connectivity for plate recognition
        if not online():
            self.logger.warning("⚠️ Sin internet - no se puede reconocer placa")
            # Log offline attempt
            authorization_service.log_access_attempt(
                license_plate=None,
                location=location,
                authorization_result=authorization_service.AuthorizationResult(
                    authorized=False,
                    reason="No internet connection for plate recognition",
                    source="network_error"
                ),
                image_path=image_path
            )
            time.sleep(config.CYCLE_INTERVAL)
            return False

        # Recognize license plate
        plate = camera.recognize_plate(image_path)
        if not plate:
            self.logger.warning(f"⚠️ No se pudo reconocer la placa en {location}")
            # Log failed plate recognition
            authorization_service.log_access_attempt(
                license_plate=None,
                location=location,
                authorization_result=authorization_service.AuthorizationResult(
                    authorized=False,
                    reason="Failed to recognize license plate",
                    source="recognition_error"
                ),
                image_path=image_path
            )
            time.sleep(config.CYCLE_INTERVAL)
            return False

        # Check authorization using the unified service
        auth_result = authorization_service.check_plate_authorization(plate, location)

        # Log the access attempt with full context
        authorization_service.log_access_attempt(
            license_plate=plate,
            location=location,
            authorization_result=auth_result,
            image_path=image_path
        )

        if auth_result.authorized:
            user_name = "Unknown"
            if auth_result.user_info:
                user_name = auth_result.user_info.get('full_name') or auth_result.user_info.get('name', 'Unknown')

            self.logger.info(f"✅ Placa [{plate}] autorizada en {location} - Usuario: {user_name}")
            return True
        else:
            self.logger.warning(f"⚠️ Placa [{plate}] no autorizada en {location} - {auth_result.reason}")
            return False

    
    def run(self):
        try:
            while True:
                if self.gate_state.is_open():
                    if (self.sensor_entrada.object_detected()
                        or self.sensor_salida.object_detected()
                        or self.sensor_pasada.object_detected()):
                        continue
                    else:
                        self.close_gate()
                        time.sleep(15)

                if self.gate_state.is_closed():
                    if self.sensor_salida.object_detected():
                        if self.is_authorized_vehicle("salida"):
                            self.open_gate()
                            time.sleep(15)
                        else: 
                            self.logger.warning("⚠️ Vehículo no autorizado intentando salir")
                            continue

                    elif self.sensor_entrada.object_detected():
                        if self.is_authorized_vehicle("entrada"):
                            self.open_gate()
                            time.sleep(15)
                        else: 
                            continue

                time.sleep(3)
                
        except KeyboardInterrupt:
            self.logger.info("🛑 Sistema detenido por usuario")
        finally:
            self.cleanup()
    
    
    def cleanup(self):
        if hasattr(self, 'sensor_entrada'):
            self.sensor_entrada.cleanup()
        if hasattr(self, 'sensor_salida'):
            self.sensor_salida.cleanup()
        if hasattr(self, 'sensor_pasada'):
            self.sensor_pasada.cleanup()
        if hasattr(self, 'camera_entrada'):
            self.camera_entrada.cleanup()
        if hasattr(self, 'camera_salida'):
            self.camera_salida.cleanup()
        if hasattr(self, 'gate_state'):
            self.gate_state.cleanup()
        if hasattr(self, 'gate_relay'):
            self.gate_relay.cleanup()
        self.logger.info("🧹 Recursos liberados")

if __name__ == "__main__":
    system = GateControlSystem()
    system.run()
