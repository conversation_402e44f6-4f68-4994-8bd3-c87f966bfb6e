#!/usr/bin/env python3
"""
Test runner for VecinoSeguro authentication and API system.

This script runs all test suites and provides a comprehensive report
of the system's functionality and security.
"""

import unittest
import sys
import os
from io import StringIO
import time

# Add current directory and parent directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import test modules
import test_authentication
import test_api_endpoints


class ColoredTextTestResult(unittest.TextTestResult):
    """Custom test result class with colored output."""

    def __init__(self, stream, descriptions, verbosity):
        super().__init__(stream, descriptions, verbosity)
        self.success_count = 0
        self._verbosity = verbosity

    def addSuccess(self, test):
        super().addSuccess(test)
        self.success_count += 1
        if self._verbosity > 1:
            self.stream.write("✅ ")
            self.stream.writeln(f"PASS: {self.getDescription(test)}")
        else:
            self.stream.write("✅")

    def addError(self, test, err):
        super().addError(test, err)
        if self._verbosity > 1:
            self.stream.write("❌ ")
            self.stream.writeln(f"ERROR: {self.getDescription(test)}")
        else:
            self.stream.write("❌")

    def addFailure(self, test, err):
        super().addFailure(test, err)
        if self._verbosity > 1:
            self.stream.write("❌ ")
            self.stream.writeln(f"FAIL: {self.getDescription(test)}")
        else:
            self.stream.write("❌")

    def addSkip(self, test, reason):
        super().addSkip(test, reason)
        if self._verbosity > 1:
            self.stream.write("⏭️  ")
            self.stream.writeln(f"SKIP: {self.getDescription(test)} ({reason})")
        else:
            self.stream.write("⏭️")


class ColoredTextTestRunner(unittest.TextTestRunner):
    """Custom test runner with colored output."""
    
    resultclass = ColoredTextTestResult
    
    def run(self, test):
        """Run the test suite with colored output."""
        print(f"\n🧪 Running {test.countTestCases()} tests...\n")
        
        result = super().run(test)
        
        # Print summary
        print(f"\n{'='*60}")
        print(f"📊 TEST SUMMARY")
        print(f"{'='*60}")
        print(f"✅ Passed: {result.success_count}")
        print(f"❌ Failed: {len(result.failures)}")
        print(f"💥 Errors: {len(result.errors)}")
        print(f"⏭️  Skipped: {len(result.skipped)}")
        print(f"📈 Total: {result.testsRun}")
        
        if result.failures:
            print(f"\n❌ FAILURES:")
            for test, traceback in result.failures:
                print(f"  • {test}: {traceback.split('AssertionError: ')[-1].split('\\n')[0]}")
                
        if result.errors:
            print(f"\n💥 ERRORS:")
            for test, traceback in result.errors:
                print(f"  • {test}: {traceback.split('\\n')[-2]}")
        
        success_rate = (result.success_count / result.testsRun * 100) if result.testsRun > 0 else 0
        print(f"\n🎯 Success Rate: {success_rate:.1f}%")
        
        if success_rate >= 90:
            print("🎉 Excellent! System is highly reliable.")
        elif success_rate >= 75:
            print("👍 Good! Minor issues to address.")
        elif success_rate >= 50:
            print("⚠️  Warning! Significant issues found.")
        else:
            print("🚨 Critical! Major problems detected.")
            
        return result


def run_test_suite(test_module, suite_name):
    """Run a specific test suite."""
    print(f"\n🔍 {suite_name}")
    print("=" * 60)

    # Load tests from module
    loader = unittest.TestLoader()
    suite = loader.loadTestsFromModule(test_module)

    # Run tests with simple runner
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)

    # Calculate success count
    success_count = result.testsRun - len(result.failures) - len(result.errors)
    result.success_count = success_count

    return result


def main():
    """Main test runner function."""
    print("🛡️  VecinoSeguro Test Suite")
    print("=" * 60)
    print("Testing authentication system and API endpoints...")
    
    start_time = time.time()
    
    # Test suites to run
    test_suites = [
        (test_authentication, "AUTHENTICATION SYSTEM TESTS"),
        (test_api_endpoints, "API ENDPOINT TESTS"),
    ]
    
    total_tests = 0
    total_passed = 0
    total_failed = 0
    total_errors = 0
    
    # Run each test suite
    for test_module, suite_name in test_suites:
        try:
            result = run_test_suite(test_module, suite_name)
            total_tests += result.testsRun
            total_passed += result.success_count
            total_failed += len(result.failures)
            total_errors += len(result.errors)
        except Exception as e:
            print(f"❌ Error running {suite_name}: {e}")
            total_errors += 1
    
    # Final summary
    end_time = time.time()
    duration = end_time - start_time
    
    print(f"\n{'='*60}")
    print(f"🏁 FINAL RESULTS")
    print(f"{'='*60}")
    print(f"⏱️  Duration: {duration:.2f} seconds")
    print(f"📊 Total Tests: {total_tests}")
    print(f"✅ Passed: {total_passed}")
    print(f"❌ Failed: {total_failed}")
    print(f"💥 Errors: {total_errors}")
    
    if total_tests > 0:
        success_rate = (total_passed / total_tests * 100)
        print(f"🎯 Overall Success Rate: {success_rate:.1f}%")
        
        if total_failed == 0 and total_errors == 0:
            print("\n🎉 ALL TESTS PASSED! System is ready for production.")
            return 0
        else:
            print(f"\n⚠️  {total_failed + total_errors} test(s) failed. Review and fix issues.")
            return 1
    else:
        print("\n❌ No tests were run.")
        return 1


if __name__ == '__main__':
    exit_code = main()
    sys.exit(exit_code)
