#!/usr/bin/env python3
"""
Integration test script for VecinoSeguro
Tests the integration between the new authentication system and existing gate control
"""

import sys
import os
from datetime import datetime, timedelta
from flask import Flask

# Add parent directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.utils.config import config
from src.models.models import db, User, Vehicle, UserLocation, AccessLog, init_db
from src.security.authorization_service import authorization_service, AuthorizationResult

def create_test_app():
    """Create Flask app for testing"""
    app = Flask(__name__)
    app.config['SECRET_KEY'] = config.SECRET_KEY
    app.config['SQLALCHEMY_DATABASE_URI'] = config.DATABASE_URL
    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = config.SQLALCHEMY_TRACK_MODIFICATIONS
    
    init_db(app)
    return app

def create_test_data(app):
    """Create test data for integration testing"""
    print("🔧 Creating test data...")
    
    with app.app_context():
        try:
            # Create test users
            admin_user = User(
                email='<EMAIL>',
                first_name='Admin',
                last_name='User',
                is_active=True,
                is_admin=True,
                email_verified=True
            )
            admin_user.set_password('admin123')
            db.session.add(admin_user)
            
            regular_user = User(
                email='<EMAIL>',
                first_name='Regular',
                last_name='User',
                is_active=True,
                is_admin=False,
                email_verified=True
            )
            regular_user.set_password('user123')
            db.session.add(regular_user)
            
            inactive_user = User(
                email='<EMAIL>',
                first_name='Inactive',
                last_name='User',
                is_active=False,
                is_admin=False,
                email_verified=True
            )
            inactive_user.set_password('inactive123')
            db.session.add(inactive_user)
            
            db.session.flush()
            
            # Create test locations
            main_location = UserLocation(
                user_id=regular_user.id,
                location_name='Main Entrance',
                location_address='123 Test Street',
                access_level='resident',
                is_active=True
            )
            db.session.add(main_location)
            
            guest_location = UserLocation(
                user_id=regular_user.id,
                location_name='Guest Area',
                location_address='456 Guest Avenue',
                access_level='guest',
                is_active=True,
                expires_at=datetime.utcnow() + timedelta(days=7)
            )
            db.session.add(guest_location)
            
            expired_location = UserLocation(
                user_id=regular_user.id,
                location_name='Expired Location',
                location_address='789 Expired Road',
                access_level='guest',
                is_active=True,
                expires_at=datetime.utcnow() - timedelta(days=1)
            )
            db.session.add(expired_location)
            
            # Create test vehicles
            active_vehicle = Vehicle(
                user_id=regular_user.id,
                license_plate='TEST123',
                make='Toyota',
                model='Camry',
                year=2020,
                color='Blue',
                vehicle_type='car',
                is_active=True,
                is_temporary=False
            )
            db.session.add(active_vehicle)
            
            expired_vehicle = Vehicle(
                user_id=regular_user.id,
                license_plate='EXP456',
                make='Honda',
                model='Civic',
                year=2019,
                color='Red',
                vehicle_type='car',
                is_active=True,
                is_temporary=True,
                expires_at=datetime.utcnow() - timedelta(days=1)
            )
            db.session.add(expired_vehicle)
            
            inactive_user_vehicle = Vehicle(
                user_id=inactive_user.id,
                license_plate='INACTIVE789',
                make='Ford',
                model='Focus',
                year=2018,
                color='White',
                vehicle_type='car',
                is_active=True,
                is_temporary=False
            )
            db.session.add(inactive_user_vehicle)
            
            admin_vehicle = Vehicle(
                user_id=admin_user.id,
                license_plate='ADMIN001',
                make='BMW',
                model='X5',
                year=2021,
                color='Black',
                vehicle_type='suv',
                is_active=True,
                is_temporary=False
            )
            db.session.add(admin_vehicle)
            
            db.session.commit()
            print("✅ Test data created successfully")
            
            return {
                'admin_user': admin_user,
                'regular_user': regular_user,
                'inactive_user': inactive_user,
                'active_vehicle': active_vehicle,
                'expired_vehicle': expired_vehicle,
                'inactive_user_vehicle': inactive_user_vehicle,
                'admin_vehicle': admin_vehicle
            }
            
        except Exception as e:
            db.session.rollback()
            print(f"❌ Error creating test data: {e}")
            raise

def test_authorization_service(test_data):
    """Test the authorization service with various scenarios"""
    print("🧪 Testing authorization service...")
    
    test_cases = [
        # (plate, location, expected_result, description)
        ('TEST123', 'entrada', True, 'Active vehicle with active user'),
        ('EXP456', 'entrada', False, 'Expired vehicle'),
        ('INACTIVE789', 'entrada', False, 'Vehicle with inactive user'),
        ('ADMIN001', 'entrada', True, 'Admin user vehicle'),
        ('NONEXISTENT', 'entrada', False, 'Non-existent vehicle'),
        ('', 'entrada', False, 'Empty license plate'),
        (None, 'entrada', False, 'None license plate'),
    ]
    
    passed = 0
    failed = 0
    
    for plate, location, expected, description in test_cases:
        try:
            result = authorization_service.check_plate_authorization(plate, location)
            
            if result.authorized == expected:
                print(f"✅ PASS: {description}")
                passed += 1
            else:
                print(f"❌ FAIL: {description}")
                print(f"   Expected: {expected}, Got: {result.authorized}")
                print(f"   Reason: {result.reason}")
                failed += 1
                
        except Exception as e:
            print(f"💥 ERROR: {description} - {e}")
            failed += 1
    
    print(f"📊 Authorization tests: {passed} passed, {failed} failed")
    return failed == 0

def test_logging_integration(test_data):
    """Test the logging integration"""
    print("🧪 Testing logging integration...")

    try:
        # Test using the authorization service directly
        result1 = authorization_service.check_plate_authorization('TEST123', 'entrada')
        authorization_service.log_access_attempt(
            license_plate='TEST123',
            location='entrada',
            authorization_result=result1,
            image_path='/test/image.jpg',
            ip_address='127.0.0.1'
        )

        # Test failed access
        result2 = authorization_service.check_plate_authorization('UNKNOWN123', 'entrada')
        authorization_service.log_access_attempt(
            license_plate='UNKNOWN123',
            location='entrada',
            authorization_result=result2,
            image_path='/test/image2.jpg',
            ip_address='127.0.0.1'
        )

        # Verify logs were created
        log_count = AccessLog.query.filter_by(ip_address='127.0.0.1').count()

        if log_count >= 2:
            print("✅ PASS: Logging integration working")
            return True
        else:
            print(f"❌ FAIL: Expected at least 2 logs, found {log_count}")
            return False

    except Exception as e:
        print(f"💥 ERROR: Logging integration test failed - {e}")
        return False

def test_backward_compatibility():
    """Test backward compatibility with JSON file"""
    print("🧪 Testing backward compatibility...")
    
    try:
        # Create a temporary JSON file
        test_json_data = {
            "JSON123": {
                "name": "JSON Test User",
                "casa": "99",
                "vehiculo": "Test Vehicle"
            }
        }
        
        # Save to temporary file
        import json
        temp_file = 'test_plates.json'
        with open(temp_file, 'w') as f:
            json.dump(test_json_data, f)
        
        # Temporarily change the config
        original_file = config.AUTHORIZED_PLATES_FILE
        config.AUTHORIZED_PLATES_FILE = temp_file
        
        # Clear cache and test
        authorization_service.json_plates_cache = {}
        authorization_service.cache_timestamp = None
        
        result = authorization_service.check_plate_authorization('JSON123', 'entrada')
        
        # Restore original config
        config.AUTHORIZED_PLATES_FILE = original_file
        
        # Clean up
        os.remove(temp_file)
        
        if result.authorized and result.source == 'json_file':
            print("✅ PASS: Backward compatibility working")
            return True
        else:
            print(f"❌ FAIL: Backward compatibility issue")
            print(f"   Authorized: {result.authorized}, Source: {result.source}")
            return False
            
    except Exception as e:
        print(f"💥 ERROR: Backward compatibility test failed - {e}")
        return False

def test_get_all_plates():
    """Test getting all authorized plates"""
    print("🧪 Testing get all plates functionality...")
    
    try:
        all_plates = authorization_service.get_all_authorized_plates()
        
        # Should have at least the test plates
        expected_plates = ['TEST123', 'EXP456', 'INACTIVE789', 'ADMIN001']
        found_plates = []
        
        for plate in expected_plates:
            if plate in all_plates:
                found_plates.append(plate)
        
        if len(found_plates) >= 3:  # At least 3 should be active
            print(f"✅ PASS: Found {len(found_plates)} expected plates")
            return True
        else:
            print(f"❌ FAIL: Only found {len(found_plates)} of {len(expected_plates)} expected plates")
            return False
            
    except Exception as e:
        print(f"💥 ERROR: Get all plates test failed - {e}")
        return False

def cleanup_test_data(app):
    """Clean up test data"""
    print("🧹 Cleaning up test data...")
    
    with app.app_context():
        try:
            # Delete test access logs
            AccessLog.query.filter_by(ip_address='127.0.0.1').delete()
            
            # Delete test vehicles
            Vehicle.query.filter(Vehicle.license_plate.in_(['TEST123', 'EXP456', 'INACTIVE789', 'ADMIN001'])).delete()
            
            # Delete test locations
            UserLocation.query.filter(UserLocation.location_name.in_(['Main Entrance', 'Guest Area', 'Expired Location'])).delete()
            
            # Delete test users
            User.query.filter(User.email.in_(['<EMAIL>', '<EMAIL>', '<EMAIL>'])).delete()
            
            db.session.commit()
            print("✅ Test data cleaned up")
            
        except Exception as e:
            db.session.rollback()
            print(f"❌ Error cleaning up test data: {e}")

def main():
    """Main test function"""
    print("🚀 Starting VecinoSeguro Integration Tests")
    print("=" * 50)
    
    app = create_test_app()
    
    try:
        # Create test data
        test_data = create_test_data(app)
        
        # Run tests
        tests_passed = 0
        total_tests = 5
        
        with app.app_context():
            if test_authorization_service(test_data):
                tests_passed += 1
            
            if test_logging_integration(test_data):
                tests_passed += 1
            
            if test_backward_compatibility():
                tests_passed += 1
            
            if test_get_all_plates():
                tests_passed += 1
        
        # Clean up
        cleanup_test_data(app)
        tests_passed += 1  # Cleanup counts as a test
        
        print("=" * 50)
        print(f"🎯 Test Results: {tests_passed}/{total_tests} tests passed")
        
        if tests_passed == total_tests:
            print("🎉 All integration tests passed!")
            return 0
        else:
            print("❌ Some tests failed!")
            return 1
            
    except Exception as e:
        print(f"💥 Integration tests failed: {e}")
        return 1

if __name__ == '__main__':
    sys.exit(main())
