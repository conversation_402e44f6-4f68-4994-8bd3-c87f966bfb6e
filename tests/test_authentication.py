#!/usr/bin/env python3
"""
Comprehensive test suite for VecinoSeguro authentication system.

This module tests all authentication-related functionality including:
- User registration and login
- Password hashing and validation
- JWT token generation and validation
- Session management
- API endpoint security
- User profile management
"""

import unittest
import json
import tempfile
import os
import sys
from datetime import datetime, timedelta
from flask import Flask
from werkzeug.security import check_password_hash

# Add parent directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import our application modules
from web_server import app
from src.models.models import db, User, Vehicle, UserLocation
from src.api.auth import generate_jwt_token, verify_jwt_token
from src.utils.config import config


class AuthenticationTestCase(unittest.TestCase):
    """Test case for authentication system functionality."""
    
    def setUp(self):
        """Set up test fixtures before each test method."""
        # Create a temporary database file
        self.db_fd, app.config['DATABASE'] = tempfile.mkstemp()
        app.config['TESTING'] = True
        app.config['WTF_CSRF_ENABLED'] = False  # Disable CSRF for testing
        app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///:memory:'
        
        self.app = app.test_client()
        self.app_context = app.app_context()
        self.app_context.push()
        
        # Create all database tables
        db.create_all()
        
        # Create test user data
        self.test_user_data = {
            'email': '<EMAIL>',
            'password': 'testpassword123',
            'password_confirm': 'testpassword123',
            'first_name': 'Test',
            'last_name': 'User',
            'phone': '555-1234'
        }
        
        self.test_admin_data = {
            'email': '<EMAIL>',
            'password': 'adminpassword123',
            'password_confirm': 'adminpassword123',
            'first_name': 'Admin',
            'last_name': 'User',
            'phone': '555-5678'
        }

    def tearDown(self):
        """Clean up after each test method."""
        db.session.remove()
        db.drop_all()
        self.app_context.pop()
        os.close(self.db_fd)
        os.unlink(app.config['DATABASE'])

    def test_user_registration_success(self):
        """Test successful user registration."""
        response = self.app.post('/auth/register',
                                data=json.dumps(self.test_user_data),
                                content_type='application/json')
        
        self.assertIn(response.status_code, [200, 201])  # Accept both 200 and 201
        data = json.loads(response.data)
        self.assertTrue(data['success'])
        self.assertIn('Usuario registrado exitosamente', data['message'])
        
        # Verify user was created in database
        user = User.query.filter_by(email=self.test_user_data['email']).first()
        self.assertIsNotNone(user)
        self.assertEqual(user.first_name, self.test_user_data['first_name'])
        self.assertEqual(user.last_name, self.test_user_data['last_name'])

    def test_user_registration_duplicate_email(self):
        """Test registration with duplicate email fails."""
        # Register user first time
        self.app.post('/auth/register',
                     data=json.dumps(self.test_user_data),
                     content_type='application/json')
        
        # Try to register same email again
        response = self.app.post('/auth/register',
                                data=json.dumps(self.test_user_data),
                                content_type='application/json')
        
        self.assertEqual(response.status_code, 400)
        data = json.loads(response.data)
        self.assertIn('ya está registrado', data['error'])

    def test_user_registration_password_mismatch(self):
        """Test registration with mismatched passwords fails."""
        invalid_data = self.test_user_data.copy()
        invalid_data['password_confirm'] = 'differentpassword'
        
        response = self.app.post('/auth/register',
                                data=json.dumps(invalid_data),
                                content_type='application/json')
        
        self.assertEqual(response.status_code, 400)
        data = json.loads(response.data)
        self.assertIn('no coinciden', data['error'])

    def test_user_login_success(self):
        """Test successful user login."""
        # Register user first
        self.app.post('/auth/register',
                     data=json.dumps(self.test_user_data),
                     content_type='application/json')
        
        # Login with correct credentials
        login_data = {
            'email': self.test_user_data['email'],
            'password': self.test_user_data['password']
        }
        
        response = self.app.post('/auth/login',
                                data=json.dumps(login_data),
                                content_type='application/json')
        
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.data)
        self.assertTrue(data['success'])
        self.assertIn('token', data)
        self.assertIn('user', data)

    def test_user_login_invalid_credentials(self):
        """Test login with invalid credentials fails."""
        # Register user first
        self.app.post('/auth/register',
                     data=json.dumps(self.test_user_data),
                     content_type='application/json')
        
        # Login with wrong password
        login_data = {
            'email': self.test_user_data['email'],
            'password': 'wrongpassword'
        }
        
        response = self.app.post('/auth/login',
                                data=json.dumps(login_data),
                                content_type='application/json')
        
        self.assertEqual(response.status_code, 401)
        data = json.loads(response.data)
        self.assertIn('incorrectos', data['error'])

    def test_password_hashing(self):
        """Test password is properly hashed."""
        # Register user
        self.app.post('/auth/register',
                     data=json.dumps(self.test_user_data),
                     content_type='application/json')
        
        # Get user from database
        user = User.query.filter_by(email=self.test_user_data['email']).first()
        
        # Password should be hashed, not stored in plain text
        self.assertNotEqual(user.password_hash, self.test_user_data['password'])
        
        # But should verify correctly
        self.assertTrue(user.check_password(self.test_user_data['password']))
        self.assertFalse(user.check_password('wrongpassword'))

    def test_jwt_token_generation_and_validation(self):
        """Test JWT token generation and validation."""
        # Register and get user
        self.app.post('/auth/register',
                     data=json.dumps(self.test_user_data),
                     content_type='application/json')
        
        user = User.query.filter_by(email=self.test_user_data['email']).first()
        
        # Generate token
        token = generate_jwt_token(user.id)
        self.assertIsNotNone(token)
        
        # Validate token
        payload = verify_jwt_token(token)
        self.assertIsNotNone(payload)
        if isinstance(payload, dict):
            self.assertEqual(payload['user_id'], user.id)
        else:
            # If payload is a User object, check the id
            self.assertEqual(payload.id, user.id)

    def test_jwt_token_expiration(self):
        """Test JWT token expiration."""
        # Register and get user
        self.app.post('/auth/register',
                     data=json.dumps(self.test_user_data),
                     content_type='application/json')
        
        user = User.query.filter_by(email=self.test_user_data['email']).first()
        
        # Generate token with short expiration (for testing)
        import jwt
        from datetime import datetime, timedelta
        
        payload = {
            'user_id': user.id,
            'exp': datetime.utcnow() - timedelta(seconds=1),  # Already expired
            'iat': datetime.utcnow()
        }
        
        expired_token = jwt.encode(payload, config.SECRET_KEY, algorithm='HS256')
        
        # Should fail validation
        result = verify_jwt_token(expired_token)
        self.assertIsNone(result)

    def test_protected_endpoint_without_auth(self):
        """Test accessing protected endpoint without authentication."""
        response = self.app.get('/api/plates')
        self.assertEqual(response.status_code, 401)

    def test_protected_endpoint_with_valid_token(self):
        """Test accessing protected endpoint with valid token."""
        # Register and login user
        self.app.post('/auth/register',
                     data=json.dumps(self.test_user_data),
                     content_type='application/json')
        
        login_response = self.app.post('/auth/login',
                                      data=json.dumps({
                                          'email': self.test_user_data['email'],
                                          'password': self.test_user_data['password']
                                      }),
                                      content_type='application/json')
        
        token = json.loads(login_response.data)['token']
        
        # Access protected endpoint with token
        response = self.app.get('/api/plates',
                               headers={'Authorization': f'Bearer {token}'})
        
        self.assertEqual(response.status_code, 200)

    def test_user_profile_update(self):
        """Test user profile update functionality."""
        # Register and login user
        self.app.post('/auth/register',
                     data=json.dumps(self.test_user_data),
                     content_type='application/json')
        
        login_response = self.app.post('/auth/login',
                                      data=json.dumps({
                                          'email': self.test_user_data['email'],
                                          'password': self.test_user_data['password']
                                      }),
                                      content_type='application/json')
        
        token = json.loads(login_response.data)['token']
        
        # Update profile
        update_data = {
            'first_name': 'Updated',
            'last_name': 'Name',
            'phone': '555-9999'
        }
        
        response = self.app.put('/auth/me',
                               data=json.dumps(update_data),
                               content_type='application/json',
                               headers={'Authorization': f'Bearer {token}'})
        
        self.assertEqual(response.status_code, 200)
        
        # Verify update in database
        user = User.query.filter_by(email=self.test_user_data['email']).first()
        self.assertEqual(user.first_name, 'Updated')
        self.assertEqual(user.last_name, 'Name')
        self.assertEqual(user.phone, '555-9999')


if __name__ == '__main__':
    # Run the tests
    unittest.main(verbosity=2)
