#!/usr/bin/env python3
"""
API endpoint tests for VecinoSeguro system.

Tests all API endpoints including:
- Vehicle/license plate management
- Access control and logging
- Statistics and reporting
- Manual gate control
- User authorization and permissions
"""

import unittest
import json
import tempfile
import os
import sys
from datetime import datetime, timedelta
from flask import Flask

# Add parent directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import our application modules
from web_server import app
from src.models.models import db, User, Vehicle, AccessLog, UserLocation
from src.api.auth import generate_jwt_token


class APIEndpointTestCase(unittest.TestCase):
    """Test case for API endpoint functionality."""
    
    def setUp(self):
        """Set up test fixtures before each test method."""
        # Create a temporary database file
        self.db_fd, app.config['DATABASE'] = tempfile.mkstemp()
        app.config['TESTING'] = True
        app.config['WTF_CSRF_ENABLED'] = False
        app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///:memory:'
        
        self.app = app.test_client()
        self.app_context = app.app_context()
        self.app_context.push()
        
        # Create all database tables
        db.create_all()
        
        # Create test users
        self.test_user = User(
            email='<EMAIL>',
            first_name='Test',
            last_name='User',
            phone='555-1234',
            is_active=True
        )
        self.test_user.set_password('password123')
        
        self.test_admin = User(
            email='<EMAIL>',
            first_name='Admin',
            last_name='User',
            phone='555-5678',
            is_active=True,
            is_admin=True
        )
        self.test_admin.set_password('admin123')
        
        db.session.add(self.test_user)
        db.session.add(self.test_admin)
        db.session.commit()
        
        # Generate tokens
        self.user_token = generate_jwt_token(self.test_user.id)
        self.admin_token = generate_jwt_token(self.test_admin.id)

    def tearDown(self):
        """Clean up after each test method."""
        db.session.remove()
        db.drop_all()
        self.app_context.pop()
        os.close(self.db_fd)
        os.unlink(app.config['DATABASE'])

    def test_get_user_plates_empty(self):
        """Test getting user plates when none exist."""
        response = self.app.get('/api/plates',
                               headers={'Authorization': f'Bearer {self.user_token}'})
        
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.data)
        self.assertEqual(data, {})

    def test_add_vehicle_success(self):
        """Test successfully adding a vehicle."""
        vehicle_data = {
            'plate': 'ABC1234',
            'name': 'Test Vehicle',
            'phone': '555-1234'
        }
        
        response = self.app.post('/api/plates',
                                data=json.dumps(vehicle_data),
                                content_type='application/json',
                                headers={'Authorization': f'Bearer {self.user_token}'})
        
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.data)
        self.assertTrue(data['success'])
        
        # Verify vehicle was created
        vehicle = Vehicle.query.filter_by(license_plate='ABC1234').first()
        self.assertIsNotNone(vehicle)
        self.assertEqual(vehicle.user_id, self.test_user.id)

    def test_add_vehicle_duplicate_plate(self):
        """Test adding duplicate license plate fails."""
        vehicle_data = {
            'plate': 'ABC1234',
            'name': 'Test Vehicle',
            'phone': '555-1234'
        }
        
        # Add vehicle first time
        self.app.post('/api/plates',
                     data=json.dumps(vehicle_data),
                     content_type='application/json',
                     headers={'Authorization': f'Bearer {self.user_token}'})
        
        # Try to add same plate again
        response = self.app.post('/api/plates',
                                data=json.dumps(vehicle_data),
                                content_type='application/json',
                                headers={'Authorization': f'Bearer {self.user_token}'})
        
        self.assertEqual(response.status_code, 400)
        data = json.loads(response.data)
        self.assertIn('ya está registrada', data['error'])

    def test_delete_own_vehicle(self):
        """Test user can delete their own vehicle."""
        # Add vehicle first
        vehicle = Vehicle(
            license_plate='ABC1234',
            user_id=self.test_user.id,
            is_active=True
        )
        db.session.add(vehicle)
        db.session.commit()
        
        # Delete vehicle
        response = self.app.delete('/api/plates/ABC1234',
                                  headers={'Authorization': f'Bearer {self.user_token}'})
        
        self.assertEqual(response.status_code, 200)
        
        # Verify vehicle was soft deleted (is_active = False)
        vehicle = Vehicle.query.filter_by(license_plate='ABC1234').first()
        self.assertIsNotNone(vehicle)
        self.assertFalse(vehicle.is_active)

    def test_delete_other_user_vehicle_fails(self):
        """Test user cannot delete another user's vehicle."""
        # Create another user and their vehicle
        other_user = User(
            email='<EMAIL>',
            first_name='Other',
            last_name='User',
            is_active=True
        )
        other_user.set_password('password123')
        db.session.add(other_user)
        db.session.commit()
        
        vehicle = Vehicle(
            license_plate='XYZ9876',
            user_id=other_user.id,
            is_active=True
        )
        db.session.add(vehicle)
        db.session.commit()
        
        # Try to delete other user's vehicle
        response = self.app.delete('/api/plates/XYZ9876',
                                  headers={'Authorization': f'Bearer {self.user_token}'})
        
        self.assertEqual(response.status_code, 403)

    def test_admin_can_delete_any_vehicle(self):
        """Test admin can delete any vehicle."""
        # Add vehicle for regular user
        vehicle = Vehicle(
            license_plate='ABC1234',
            user_id=self.test_user.id,
            is_active=True
        )
        db.session.add(vehicle)
        db.session.commit()
        
        # Admin deletes vehicle
        response = self.app.delete('/api/plates/ABC1234',
                                  headers={'Authorization': f'Bearer {self.admin_token}'})
        
        self.assertEqual(response.status_code, 200)

    def test_get_stats_requires_admin(self):
        """Test statistics endpoint requires admin access."""
        # Regular user should be denied
        response = self.app.get('/api/stats',
                               headers={'Authorization': f'Bearer {self.user_token}'})
        
        self.assertEqual(response.status_code, 403)
        
        # Admin should have access
        response = self.app.get('/api/stats',
                               headers={'Authorization': f'Bearer {self.admin_token}'})
        
        self.assertEqual(response.status_code, 200)

    def test_get_logs_requires_admin(self):
        """Test access logs endpoint requires admin access."""
        # Regular user should be denied
        response = self.app.get('/api/logs',
                               headers={'Authorization': f'Bearer {self.user_token}'})
        
        self.assertEqual(response.status_code, 403)
        
        # Admin should have access
        response = self.app.get('/api/logs',
                               headers={'Authorization': f'Bearer {self.admin_token}'})
        
        self.assertEqual(response.status_code, 200)

    def test_manual_gate_control_requires_auth(self):
        """Test manual gate control requires authentication."""
        # No auth should fail
        response = self.app.post('/api/open-gate',
                                data=json.dumps({'duration': 10}),
                                content_type='application/json')
        
        self.assertEqual(response.status_code, 401)
        
        # With auth should work
        response = self.app.post('/api/open-gate',
                                data=json.dumps({'duration': 10}),
                                content_type='application/json',
                                headers={'Authorization': f'Bearer {self.user_token}'})
        
        self.assertEqual(response.status_code, 200)

    def test_rate_limiting_on_sensitive_endpoints(self):
        """Test rate limiting is applied to sensitive endpoints."""
        # This test would need to be adjusted based on actual rate limits
        # For now, just verify the endpoint responds correctly once
        response = self.app.post('/api/open-gate',
                                data=json.dumps({'duration': 5}),
                                content_type='application/json',
                                headers={'Authorization': f'Bearer {self.user_token}'})
        
        self.assertEqual(response.status_code, 200)

    def test_invalid_license_plate_format(self):
        """Test invalid license plate formats are rejected."""
        invalid_plates = [
            '',  # Empty
            'A',  # Too short
            'ABCDEFGHIJK',  # Too long
            'ABC-1234',  # Invalid characters
            '123',  # Numbers only
        ]
        
        for plate in invalid_plates:
            vehicle_data = {
                'plate': plate,
                'name': 'Test Vehicle',
                'phone': '555-1234'
            }
            
            response = self.app.post('/api/plates',
                                    data=json.dumps(vehicle_data),
                                    content_type='application/json',
                                    headers={'Authorization': f'Bearer {self.user_token}'})
            
            self.assertNotEqual(response.status_code, 200, 
                              f"Invalid plate '{plate}' should be rejected")

    def test_access_log_creation(self):
        """Test access log creation and retrieval."""
        # Create a test access log
        log = AccessLog(
            license_plate='ABC1234',
            location='entrada',
            access_granted=True,
            timestamp=datetime.utcnow(),
            image_path='test.jpg'
        )
        db.session.add(log)
        db.session.commit()
        
        # Retrieve logs as admin
        response = self.app.get('/api/logs?hours=24',
                               headers={'Authorization': f'Bearer {self.admin_token}'})
        
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.data)
        self.assertEqual(len(data), 1)
        self.assertEqual(data[0]['placa'], 'ABC1234')
        self.assertTrue(data[0]['autorizado'])

    def test_statistics_calculation(self):
        """Test statistics calculation with sample data."""
        # Create sample access logs
        logs = [
            AccessLog(license_plate='ABC1234', location='entrada', access_granted=True, timestamp=datetime.utcnow()),
            AccessLog(license_plate='XYZ9876', location='entrada', access_granted=False, timestamp=datetime.utcnow()),
            AccessLog(license_plate='ABC1234', location='salida', access_granted=True, timestamp=datetime.utcnow()),
        ]
        
        for log in logs:
            db.session.add(log)
        db.session.commit()
        
        # Get statistics
        response = self.app.get('/api/stats',
                               headers={'Authorization': f'Bearer {self.admin_token}'})
        
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.data)
        
        self.assertEqual(data['total_accesos'], 3)
        self.assertEqual(data['autorizados'], 2)
        self.assertEqual(data['no_autorizados'], 1)
        self.assertEqual(data['entradas'], 2)
        self.assertEqual(data['salidas'], 1)


if __name__ == '__main__':
    # Run the tests
    unittest.main(verbosity=2)
