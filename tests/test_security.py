#!/usr/bin/env python3
"""
Security Testing Suite for VecinoSeguro
Tests various security features and protections
"""

import requests
import time
import json
import sys
from datetime import datetime

class SecurityTester:
    """Comprehensive security testing suite"""
    
    def __init__(self, base_url='http://127.0.0.1:8082'):
        self.base_url = base_url
        self.session = requests.Session()
        self.test_results = []
    
    def log_test(self, test_name, passed, details=None):
        """Log test result"""
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"{status}: {test_name}")
        if details:
            print(f"   Details: {details}")
        
        self.test_results.append({
            'test': test_name,
            'passed': passed,
            'details': details,
            'timestamp': datetime.now().isoformat()
        })
    
    def test_security_headers(self):
        """Test security headers implementation"""
        print("\n🔒 Testing Security Headers...")
        
        try:
            response = self.session.get(f"{self.base_url}/")
            headers = response.headers
            
            # Test required security headers
            required_headers = {
                'X-Content-Type-Options': 'nosniff',
                'X-Frame-Options': 'DENY',
                'X-XSS-Protection': '1; mode=block',
                'Content-Security-Policy': None,  # Just check presence
                'Referrer-Policy': 'strict-origin-when-cross-origin'
            }
            
            for header, expected_value in required_headers.items():
                if header in headers:
                    if expected_value is None or headers[header] == expected_value:
                        self.log_test(f"Security header {header}", True)
                    else:
                        self.log_test(f"Security header {header}", False, 
                                    f"Expected: {expected_value}, Got: {headers[header]}")
                else:
                    self.log_test(f"Security header {header}", False, "Header missing")
            
            # Test CSP header specifically
            csp = headers.get('Content-Security-Policy', '')
            if 'default-src' in csp and "'self'" in csp:
                self.log_test("CSP default-src directive", True)
            else:
                self.log_test("CSP default-src directive", False, f"CSP: {csp[:100]}...")
            
        except Exception as e:
            self.log_test("Security headers test", False, str(e))
    
    def test_rate_limiting(self):
        """Test rate limiting functionality"""
        print("\n⏱️ Testing Rate Limiting...")
        
        try:
            # Make rapid requests to trigger rate limiting
            responses = []
            for i in range(15):
                response = self.session.get(f"{self.base_url}/api/stats")
                responses.append(response.status_code)
                time.sleep(0.1)  # Small delay
            
            # Check if any requests were rate limited (429 status)
            rate_limited = any(status == 429 for status in responses)
            
            if rate_limited:
                self.log_test("Rate limiting active", True, f"Got 429 status in {responses.count(429)} requests")
            else:
                self.log_test("Rate limiting active", False, "No rate limiting detected")
                
        except Exception as e:
            self.log_test("Rate limiting test", False, str(e))
    
    def test_sql_injection_protection(self):
        """Test SQL injection protection"""
        print("\n💉 Testing SQL Injection Protection...")
        
        sql_payloads = [
            "' OR '1'='1",
            "'; DROP TABLE users; --",
            "' UNION SELECT * FROM users --",
            "admin'--",
            "' OR 1=1 --"
        ]
        
        for payload in sql_payloads:
            try:
                # Test in login endpoint
                response = self.session.post(f"{self.base_url}/auth/login", 
                                           json={'email': payload, 'password': 'test'})
                
                # Should not return 500 error (should be handled gracefully)
                if response.status_code != 500:
                    self.log_test(f"SQL injection protection ({payload[:20]}...)", True)
                else:
                    self.log_test(f"SQL injection protection ({payload[:20]}...)", False, 
                                f"Server error: {response.status_code}")
                    
            except Exception as e:
                self.log_test(f"SQL injection test ({payload[:20]}...)", False, str(e))
    
    def test_xss_protection(self):
        """Test XSS protection"""
        print("\n🚨 Testing XSS Protection...")
        
        xss_payloads = [
            "<script>alert('xss')</script>",
            "javascript:alert('xss')",
            "<img src=x onerror=alert('xss')>",
            "<svg onload=alert('xss')>",
            "';alert('xss');//"
        ]
        
        for payload in xss_payloads:
            try:
                # Test in registration endpoint
                response = self.session.post(f"{self.base_url}/auth/register", 
                                           json={
                                               'email': '<EMAIL>',
                                               'first_name': payload,
                                               'last_name': 'Test',
                                               'password': 'test123'
                                           })
                
                # Check if payload is properly sanitized in response
                response_text = response.text.lower()
                if '<script>' not in response_text and 'javascript:' not in response_text:
                    self.log_test(f"XSS protection ({payload[:20]}...)", True)
                else:
                    self.log_test(f"XSS protection ({payload[:20]}...)", False, 
                                "Payload not properly sanitized")
                    
            except Exception as e:
                self.log_test(f"XSS test ({payload[:20]}...)", False, str(e))
    
    def test_csrf_protection(self):
        """Test CSRF protection"""
        print("\n🛡️ Testing CSRF Protection...")
        
        try:
            # Try to make a state-changing request without CSRF token
            response = self.session.post(f"{self.base_url}/api/vehicles", 
                                       json={
                                           'license_plate': 'TEST123',
                                           'make': 'Test',
                                           'model': 'Car'
                                       })
            
            # Should be rejected due to missing CSRF token or authentication
            if response.status_code in [401, 403]:
                self.log_test("CSRF protection active", True, f"Request rejected: {response.status_code}")
            else:
                self.log_test("CSRF protection active", False, 
                            f"Request not properly protected: {response.status_code}")
                
        except Exception as e:
            self.log_test("CSRF protection test", False, str(e))
    
    def test_session_security(self):
        """Test session security features"""
        print("\n🔐 Testing Session Security...")
        
        try:
            # Test session cookie attributes
            response = self.session.get(f"{self.base_url}/")
            
            # Check Set-Cookie headers for security attributes
            set_cookie = response.headers.get('Set-Cookie', '')
            
            security_attributes = ['HttpOnly', 'Secure', 'SameSite']
            for attr in security_attributes:
                if attr.lower() in set_cookie.lower():
                    self.log_test(f"Session cookie {attr} attribute", True)
                else:
                    self.log_test(f"Session cookie {attr} attribute", False, 
                                f"Set-Cookie: {set_cookie}")
                    
        except Exception as e:
            self.log_test("Session security test", False, str(e))
    
    def test_input_validation(self):
        """Test input validation"""
        print("\n✅ Testing Input Validation...")
        
        # Test various invalid inputs
        invalid_inputs = [
            {'email': 'invalid-email', 'field': 'email'},
            {'license_plate': 'TOOLONGPLATE123456', 'field': 'license_plate'},
            {'phone': 'invalid-phone-123abc', 'field': 'phone'},
            {'first_name': 'A' * 200, 'field': 'first_name'},  # Too long
            {'password': '123', 'field': 'password'},  # Too short
        ]
        
        for invalid_input in invalid_inputs:
            try:
                field = invalid_input.pop('field')
                test_data = {
                    'email': '<EMAIL>',
                    'first_name': 'Test',
                    'last_name': 'User',
                    'password': 'test123456'
                }
                test_data.update(invalid_input)
                
                response = self.session.post(f"{self.base_url}/auth/register", json=test_data)
                
                # Should return validation error (400)
                if response.status_code == 400:
                    self.log_test(f"Input validation ({field})", True)
                else:
                    self.log_test(f"Input validation ({field})", False, 
                                f"Expected 400, got {response.status_code}")
                    
            except Exception as e:
                self.log_test(f"Input validation test ({field})", False, str(e))
    
    def test_authentication_security(self):
        """Test authentication security features"""
        print("\n🔑 Testing Authentication Security...")
        
        try:
            # Test multiple failed login attempts
            failed_attempts = 0
            for i in range(6):  # Try 6 failed logins
                response = self.session.post(f"{self.base_url}/auth/login", 
                                           json={'email': '<EMAIL>', 'password': 'wrongpassword'})
                if response.status_code == 401:
                    failed_attempts += 1
                elif response.status_code == 423:  # Account locked
                    self.log_test("Account lockout protection", True, f"Account locked after {failed_attempts} attempts")
                    break
                time.sleep(0.5)
            else:
                self.log_test("Account lockout protection", False, "No account lockout detected")
            
            # Test password strength requirements
            weak_passwords = ['123', 'password', 'abc123']
            for weak_password in weak_passwords:
                response = self.session.post(f"{self.base_url}/auth/register", 
                                           json={
                                               'email': f'test{weak_password}@test.com',
                                               'first_name': 'Test',
                                               'last_name': 'User',
                                               'password': weak_password
                                           })
                
                if response.status_code == 400:
                    self.log_test(f"Password strength ({weak_password})", True)
                    break
            else:
                self.log_test("Password strength validation", False, "Weak passwords accepted")
                
        except Exception as e:
            self.log_test("Authentication security test", False, str(e))
    
    def test_api_security(self):
        """Test API endpoint security"""
        print("\n🔌 Testing API Security...")
        
        # Test unauthorized access to protected endpoints
        protected_endpoints = [
            '/api/users',
            '/api/vehicles',
            '/api/logs',
            '/api/stats',
            '/api/admin/users'
        ]
        
        for endpoint in protected_endpoints:
            try:
                response = self.session.get(f"{self.base_url}{endpoint}")
                
                # Should require authentication (401) or authorization (403)
                if response.status_code in [401, 403]:
                    self.log_test(f"API protection ({endpoint})", True)
                else:
                    self.log_test(f"API protection ({endpoint})", False, 
                                f"Unprotected endpoint: {response.status_code}")
                    
            except Exception as e:
                self.log_test(f"API security test ({endpoint})", False, str(e))
    
    def run_all_tests(self):
        """Run all security tests"""
        print("🚀 Starting VecinoSeguro Security Test Suite")
        print("=" * 50)
        
        start_time = time.time()
        
        # Run all test categories
        self.test_security_headers()
        self.test_rate_limiting()
        self.test_sql_injection_protection()
        self.test_xss_protection()
        self.test_csrf_protection()
        self.test_session_security()
        self.test_input_validation()
        self.test_authentication_security()
        self.test_api_security()
        
        # Calculate results
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result['passed'])
        failed_tests = total_tests - passed_tests
        
        duration = time.time() - start_time
        
        print("=" * 50)
        print(f"🎯 Security Test Results:")
        print(f"   Total Tests: {total_tests}")
        print(f"   Passed: {passed_tests}")
        print(f"   Failed: {failed_tests}")
        print(f"   Success Rate: {(passed_tests/total_tests)*100:.1f}%")
        print(f"   Duration: {duration:.2f} seconds")
        
        if failed_tests == 0:
            print("🎉 All security tests passed!")
            return 0
        else:
            print("⚠️ Some security tests failed. Review the results above.")
            return 1

def main():
    """Main test function"""
    import argparse
    
    parser = argparse.ArgumentParser(description='VecinoSeguro Security Test Suite')
    parser.add_argument('--url', default='http://127.0.0.1:8080', 
                       help='Base URL of the application (default: http://127.0.0.1:8080)')
    parser.add_argument('--output', help='Output file for test results (JSON format)')
    
    args = parser.parse_args()
    
    tester = SecurityTester(args.url)
    result = tester.run_all_tests()
    
    # Save results if output file specified
    if args.output:
        with open(args.output, 'w') as f:
            json.dump(tester.test_results, f, indent=2)
        print(f"📄 Test results saved to {args.output}")
    
    return result

if __name__ == '__main__':
    sys.exit(main())
