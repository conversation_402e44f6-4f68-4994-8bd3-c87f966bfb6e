<!DOCTYPE html>
<html lang="es">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>VecinoSeguro - Control de Acceso</title>
    <link
      href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap"
      rel="stylesheet"
    />
    <link
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
      rel="stylesheet"
    />
    <link
      rel="stylesheet"
      href="{{ url_for('static', filename='css/dashboard.css') }}"
    />
  </head>
  <body>
    <!-- Navigation -->
    <nav class="navbar">
      <div class="navbar-content">
        <a href="/" class="logo">
          <i class="fas fa-shield-alt"></i>
          VecinoSeguro
        </a>
        <div class="user-menu">
          <div class="nav-links">
            <a href="/" class="active">Dashboard</a>
            <a href="/profile">Mi Perfil</a>
          </div>
          <button class="mobile-menu-toggle" onclick="toggleMobileMenu()">
            <i class="fas fa-bars"></i>
          </button>
        </div>
      </div>
    </nav>

    <div class="container">
      <div class="header">
        <div class="header-content">
          <h1><i class="fas fa-shield-alt"></i> VecinoSeguro</h1>
          <p>Sistema inteligente de control de acceso vehicular</p>
        </div>
      </div>

      <div class="stats" id="stats">
        <!-- Estadísticas se cargan aquí -->
      </div>

      <div class="tabs">
        <button class="tab active" onclick="showTab('logs')">
          <i class="fas fa-chart-line"></i> Registros de Acceso
        </button>
        <button class="tab" onclick="showTab('plates')">
          <i class="fas fa-car"></i> Placas Autorizadas
        </button>
        <button class="tab" onclick="showTab('control')">
          <i class="fas fa-cogs"></i> Control Manual
        </button>
      </div>

      <div id="logs-tab" class="tab-content">
        <div class="section-header">
          <h3 class="section-title">
            <i class="fas fa-history"></i>
            Registros de Acceso
          </h3>
          <div class="filter-controls">
            <label
              for="hours-filter"
              style="font-weight: 500; color: var(--gray-700)"
              >Período:</label
            >
            <select id="hours-filter" onchange="loadLogs()" class="form-group">
              <option value="1">Última hora</option>
              <option value="6">Últimas 6 horas</option>
              <option value="24" selected>Últimas 24 horas</option>
              <option value="168">Última semana</option>
            </select>
            <button class="btn btn-secondary" onclick="loadLogs()">
              <i class="fas fa-sync-alt"></i> Actualizar
            </button>
          </div>
        </div>
        <div class="table-container" id="logs-container">
          <!-- Logs se cargan aquí -->
        </div>
      </div>

      <div id="plates-tab" class="tab-content" style="display: none">
        <div class="section-header">
          <h3 class="section-title">
            <i class="fas fa-list-alt"></i>
            Gestión de Placas Autorizadas
          </h3>
        </div>

        <div class="add-form">
          <h4
            style="
              margin-bottom: 1.5rem;
              color: var(--gray-800);
              font-weight: 600;
            "
          >
            <i class="fas fa-plus-circle"></i> Agregar Nueva Placa
          </h4>
          <div class="form-row">
            <div class="form-group">
              <label for="new-plate">Número de Placa:</label>
              <input
                type="text"
                id="new-plate"
                placeholder="ABC1234"
                style="text-transform: uppercase"
                maxlength="8"
              />
            </div>
            <div class="form-group">
              <label for="new-name">Nombre del Propietario:</label>
              <input type="text" id="new-name" placeholder="Juan Pérez" />
            </div>
            <div class="form-group">
              <label for="new-phone">Teléfono (opcional):</label>
              <input type="tel" id="new-phone" placeholder="555-1234" />
            </div>
            <button class="btn btn-primary" onclick="addPlate()">
              <i class="fas fa-plus"></i> Agregar Placa
            </button>
          </div>
        </div>

        <div class="table-container" id="plates-container">
          <!-- Placas se cargan aquí -->
        </div>
      </div>

      <div id="control-tab" class="tab-content" style="display: none">
        <div class="section-header">
          <h3 class="section-title">
            <i class="fas fa-door-open"></i>
            Control Manual del Portón
          </h3>
        </div>

        <div class="control-panel">
          <h4
            style="
              margin-bottom: 1rem;
              color: var(--gray-800);
              font-weight: 600;
            "
          >
            <i class="fas fa-exclamation-triangle"></i> Apertura Manual de
            Emergencia
          </h4>
          <p style="margin-bottom: 2rem; color: var(--gray-600)">
            Use esta función únicamente en casos de emergencia o mantenimiento
            programado
          </p>

          <div style="margin-bottom: 2rem">
            <div class="form-group" style="max-width: 300px; margin: 0 auto">
              <label for="duration">Duración de apertura:</label>
              <select id="duration">
                <option value="5">5 segundos</option>
                <option value="10" selected>10 segundos</option>
                <option value="15">15 segundos</option>
                <option value="20">20 segundos</option>
                <option value="30">30 segundos</option>
              </select>
            </div>
          </div>

          <button
            class="btn btn-danger"
            onclick="openGateManually()"
            style="font-size: 1rem; padding: 1rem 2rem"
          >
            <i class="fas fa-door-open"></i> Abrir Portón Ahora
          </button>
        </div>

        <div class="warning-box">
          <strong
            ><i class="fas fa-exclamation-triangle"></i> Advertencia
            Importante:</strong
          >
          El portón se abrirá inmediatamente al presionar el botón. Asegúrese de
          que el área esté completamente despejada y que no haya vehículos o
          personas en la zona de apertura.
        </div>
      </div>
    </div>
    <script src="/static/js/dashboard.js"></script>
  </body>
</html>
