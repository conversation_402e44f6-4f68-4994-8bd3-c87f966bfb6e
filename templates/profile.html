<!DOCTYPE html>
<html lang="es">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>VecinoSeguro - Mi Perfil</title>
    <link
      href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap"
      rel="stylesheet"
    />
    <link
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
      rel="stylesheet"
    />
    <link
      rel="stylesheet"
      href="{{ url_for('static', filename='css/profile.css') }}"
    />
  </head>
  <body>
    <!-- Navigation -->
    <nav class="navbar">
      <div class="navbar-content">
        <div class="logo">
          <i class="fas fa-shield-alt"></i>
          VecinoSeguro
        </div>
        <div class="user-menu">
          <div class="nav-links">
            <a href="/">Dashboard</a>
            <a href="/profile" class="active">Mi Perfil</a>
          </div>
          <button class="mobile-menu-toggle" onclick="toggleMobileMenu()">
            <i class="fas fa-bars"></i>
          </button>
        </div>
      </div>
    </nav>

    <div class="container">
      <!-- Page Header -->
      <div class="page-header">
        <h1 class="page-title">Mi Perfil</h1>
        <p class="page-subtitle">
          Gestiona tu información personal, vehículos y ubicaciones
        </p>
      </div>

      <!-- Alert Container -->
      <div id="alert-container"></div>

      <!-- Profile Grid -->
      <div class="profile-grid">
        <!-- Profile Summary -->
        <div class="profile-card">
          <div class="profile-avatar">
            <div class="avatar" id="userAvatar">
              <i class="fas fa-user"></i>
            </div>
            <div class="profile-name" id="userName">Cargando...</div>
            <div class="profile-email" id="userEmail">Cargando...</div>
            <span class="badge badge-success" id="userStatus">Activo</span>
          </div>

          <div class="profile-stats">
            <div class="stat-item">
              <div class="stat-number" id="vehicleCount">0</div>
              <div class="stat-label">Vehículos</div>
            </div>
            <div class="stat-item">
              <div class="stat-number" id="locationCount">0</div>
              <div class="stat-label">Ubicaciones</div>
            </div>
          </div>

          <!-- Logout Button -->
          <div style="margin-top: 2rem; text-align: center">
            <button
              class="btn btn-danger"
              onclick="logout()"
              style="width: 100%"
            >
              <i class="fas fa-sign-out-alt"></i>
              Cerrar Sesión
            </button>
          </div>
        </div>

        <!-- Profile Form -->
        <div class="profile-card">
          <h3 class="section-title">
            <i class="fas fa-user-edit"></i>
            Información Personal
          </h3>

          <form id="profileForm">
            <div class="form-row">
              <div class="form-group">
                <label for="first_name">Nombre</label>
                <input type="text" id="first_name" name="first_name" required />
              </div>
              <div class="form-group">
                <label for="last_name">Apellido</label>
                <input type="text" id="last_name" name="last_name" required />
              </div>
            </div>

            <div class="form-group">
              <label for="email">Correo Electrónico</label>
              <input type="email" id="email" name="email" disabled />
            </div>

            <div class="form-group">
              <label for="phone">Teléfono</label>
              <input type="tel" id="phone" name="phone" />
            </div>

            <div style="display: flex; gap: 1rem">
              <button type="submit" class="btn btn-primary">
                <i class="fas fa-spinner loading" id="profileSpinner"></i>
                <span id="profileBtnText">Guardar Cambios</span>
              </button>
              <button
                type="button"
                class="btn btn-secondary"
                onclick="showPasswordModal()"
              >
                <i class="fas fa-key"></i>
                Cambiar Contraseña
              </button>
            </div>
          </form>
        </div>
      </div>

      <!-- Vehicles Section -->
      <div class="vehicles-section">
        <div
          style="
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
          "
        >
          <h3 class="section-title" style="margin-bottom: 0">
            <i class="fas fa-car"></i>
            Mis Vehículos
          </h3>
          <button class="btn btn-primary" onclick="showVehicleModal()">
            <i class="fas fa-plus"></i>
            Agregar Vehículo
          </button>
        </div>

        <div id="vehiclesList">
          <div
            style="text-align: center; padding: 2rem; color: var(--gray-500)"
          >
            <i
              class="fas fa-car"
              style="font-size: 3rem; margin-bottom: 1rem; opacity: 0.3"
            ></i>
            <p>Cargando vehículos...</p>
          </div>
        </div>
      </div>

      <!-- Locations Section -->
      <div class="locations-section">
        <h3 class="section-title">
          <i class="fas fa-map-marker-alt"></i>
          Mis Ubicaciones
        </h3>

        <div id="locationsList">
          <div
            style="text-align: center; padding: 2rem; color: var(--gray-500)"
          >
            <i
              class="fas fa-map-marker-alt"
              style="font-size: 3rem; margin-bottom: 1rem; opacity: 0.3"
            ></i>
            <p>Cargando ubicaciones...</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Vehicle Modal -->
    <div class="modal" id="vehicleModal">
      <div class="modal-content">
        <div class="modal-header">
          <h3 class="modal-title" id="vehicleModalTitle">Agregar Vehículo</h3>
          <button class="close-btn" onclick="closeVehicleModal()">
            &times;
          </button>
        </div>

        <form id="vehicleForm">
          <input type="hidden" id="vehicleId" name="vehicleId" />

          <div class="form-group">
            <label for="license_plate">Placa *</label>
            <input
              type="text"
              id="license_plate"
              name="license_plate"
              required
              style="text-transform: uppercase"
            />
          </div>

          <div class="form-row">
            <div class="form-group">
              <label for="make">Marca</label>
              <input type="text" id="make" name="make" />
            </div>
            <div class="form-group">
              <label for="model">Modelo</label>
              <input type="text" id="model" name="model" />
            </div>
          </div>

          <div class="form-row">
            <div class="form-group">
              <label for="year">Año</label>
              <input
                type="number"
                id="year"
                name="year"
                min="1900"
                max="2030"
              />
            </div>
            <div class="form-group">
              <label for="color">Color</label>
              <input type="text" id="color" name="color" />
            </div>
          </div>

          <div class="form-group">
            <label for="vehicle_type">Tipo de Vehículo</label>
            <select
              id="vehicle_type"
              name="vehicle_type"
              style="
                width: 100%;
                padding: 0.75rem 1rem;
                border: 2px solid var(--gray-200);
                border-radius: 0.5rem;
              "
            >
              <option value="car">Automóvil</option>
              <option value="motorcycle">Motocicleta</option>
              <option value="truck">Camioneta</option>
              <option value="van">Van</option>
              <option value="suv">SUV</option>
            </select>
          </div>

          <div style="display: flex; gap: 1rem; margin-top: 2rem">
            <button type="submit" class="btn btn-success">
              <i class="fas fa-spinner loading" id="vehicleSpinner"></i>
              <span id="vehicleBtnText">Guardar Vehículo</span>
            </button>
            <button
              type="button"
              class="btn btn-secondary"
              onclick="closeVehicleModal()"
            >
              Cancelar
            </button>
          </div>
        </form>
      </div>
    </div>

    <!-- Password Modal -->
    <div class="modal" id="passwordModal">
      <div class="modal-content">
        <div class="modal-header">
          <h3 class="modal-title">Cambiar Contraseña</h3>
          <button class="close-btn" onclick="closePasswordModal()">
            &times;
          </button>
        </div>

        <form id="passwordForm">
          <div class="form-group">
            <label for="current_password">Contraseña Actual *</label>
            <input
              type="password"
              id="current_password"
              name="current_password"
              required
            />
          </div>

          <div class="form-group">
            <label for="new_password">Nueva Contraseña *</label>
            <input
              type="password"
              id="new_password"
              name="new_password"
              required
            />
          </div>

          <div class="form-group">
            <label for="confirm_password">Confirmar Nueva Contraseña *</label>
            <input
              type="password"
              id="confirm_password"
              name="confirm_password"
              required
            />
          </div>

          <div style="display: flex; gap: 1rem; margin-top: 2rem">
            <button type="submit" class="btn btn-primary">
              <i class="fas fa-spinner loading" id="passwordSpinner"></i>
              <span id="passwordBtnText">Cambiar Contraseña</span>
            </button>
            <button
              type="button"
              class="btn btn-secondary"
              onclick="closePasswordModal()"
            >
              Cancelar
            </button>
          </div>
        </form>
      </div>
    </div>

    <script src="/static/js/profile.js?v=2.0"></script>
  </body>
</html>
