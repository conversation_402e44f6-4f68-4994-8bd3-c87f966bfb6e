#!/usr/bin/env python3
"""
Database initialization script for VecinoSeguro
This script creates the database tables and migrates existing data
"""

import os
import sys
import json
from datetime import datetime

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from flask import Flask
from config import config
from models import db, init_db, migrate_existing_plates, User, UserLocation, Vehicle

def create_app():
    """Create Flask app for database operations"""
    app = Flask(__name__)
    
    # Configure app
    app.config['SQLALCHEMY_DATABASE_URI'] = config.DATABASE_URL
    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = config.SQLALCHEMY_TRACK_MODIFICATIONS
    app.config['SECRET_KEY'] = config.SECRET_KEY
    
    return app

def create_sample_data():
    """Create sample data for testing"""
    try:
        # Check if we already have users (besides admin)
        user_count = User.query.filter(User.email != '<EMAIL>').count()
        if user_count > 0:
            print("Sample data already exists, skipping...")
            return
        
        print("Creating sample data...")
        
        # Create sample users
        users_data = [
            {
                'email': '<EMAIL>',
                'first_name': 'Juan',
                'last_name': 'Pérez',
                'phone': '+1234567890',
                'password': 'password123'
            },
            {
                'email': '<EMAIL>',
                'first_name': 'María',
                'last_name': 'García',
                'phone': '+1234567891',
                'password': 'password123'
            },
            {
                'email': '<EMAIL>',
                'first_name': 'Carlos',
                'last_name': 'Rodríguez',
                'phone': '+1234567892',
                'password': 'password123'
            }
        ]
        
        created_users = []
        for user_data in users_data:
            user = User(
                email=user_data['email'],
                first_name=user_data['first_name'],
                last_name=user_data['last_name'],
                phone=user_data['phone'],
                is_active=True,
                email_verified=True
            )
            user.set_password(user_data['password'])
            db.session.add(user)
            created_users.append(user)
        
        db.session.flush()  # Get user IDs
        
        # Create sample locations
        locations_data = [
            'Residencial Los Pinos',
            'Condominio Vista Hermosa',
            'Urbanización El Bosque'
        ]
        
        for i, user in enumerate(created_users):
            # Each user has access to one main location
            location = UserLocation(
                user_id=user.id,
                location_name=locations_data[i],
                location_address=f"Calle {i+1} #{(i+1)*100}, Ciudad",
                access_level='resident',
                is_active=True
            )
            db.session.add(location)
        
        # Create sample vehicles
        vehicles_data = [
            # Juan's vehicles
            [
                {'plate': 'ABC123', 'make': 'Toyota', 'model': 'Corolla', 'year': 2020, 'color': 'Blanco'},
                {'plate': 'DEF456', 'make': 'Honda', 'model': 'Civic', 'year': 2019, 'color': 'Azul'}
            ],
            # María's vehicles
            [
                {'plate': 'GHI789', 'make': 'Nissan', 'model': 'Sentra', 'year': 2021, 'color': 'Rojo'},
            ],
            # Carlos's vehicles
            [
                {'plate': 'JKL012', 'make': 'Chevrolet', 'model': 'Spark', 'year': 2018, 'color': 'Negro'},
                {'plate': 'MNO345', 'make': 'Ford', 'model': 'Focus', 'year': 2022, 'color': 'Gris'},
                {'plate': 'PQR678', 'make': 'Hyundai', 'model': 'Elantra', 'year': 2020, 'color': 'Blanco', 'temporary': True}
            ]
        ]
        
        for i, user in enumerate(created_users):
            for vehicle_data in vehicles_data[i]:
                vehicle = Vehicle(
                    user_id=user.id,
                    license_plate=vehicle_data['plate'],
                    make=vehicle_data['make'],
                    model=vehicle_data['model'],
                    year=vehicle_data['year'],
                    color=vehicle_data['color'],
                    is_active=True,
                    is_temporary=vehicle_data.get('temporary', False)
                )
                if vehicle_data.get('temporary'):
                    # Temporary vehicle expires in 30 days
                    from datetime import timedelta
                    vehicle.expires_at = datetime.utcnow() + timedelta(days=30)
                
                db.session.add(vehicle)
        
        db.session.commit()
        print(f"✅ Created {len(created_users)} sample users with locations and vehicles")
        
        # Print sample credentials
        print("\n📋 Sample User Credentials:")
        print("=" * 50)
        for user_data in users_data:
            print(f"Email: {user_data['email']}")
            print(f"Password: {user_data['password']}")
            print("-" * 30)
        
    except Exception as e:
        db.session.rollback()
        print(f"❌ Error creating sample data: {e}")

def main():
    """Main initialization function"""
    print("🚀 Initializing VecinoSeguro Database...")
    
    app = create_app()
    
    with app.app_context():
        try:
            # Initialize database and create tables
            print("📊 Creating database tables...")
            init_db(app)
            
            # Migrate existing plates if they exist
            print("🔄 Migrating existing license plates...")
            migrate_existing_plates()
            
            # Create sample data for development/testing
            if len(sys.argv) > 1 and sys.argv[1] == '--sample-data':
                create_sample_data()
            
            print("✅ Database initialization completed successfully!")
            print(f"📁 Database location: {config.DATABASE_URL}")
            
            # Show admin credentials
            admin_user = User.query.filter_by(is_admin=True).first()
            if admin_user:
                print(f"\n🔑 Admin Credentials:")
                print(f"Email: {admin_user.email}")
                print(f"Password: admin123 (CHANGE THIS IN PRODUCTION!)")
            
        except Exception as e:
            print(f"❌ Database initialization failed: {e}")
            sys.exit(1)

if __name__ == '__main__':
    main()
