from flask_sqlalchemy import SQLAlchemy
from flask_login import UserMixin
from werkzeug.security import generate_password_hash, check_password_hash
from datetime import datetime
import json

db = SQLAlchemy()

class User(UserMixin, db.Model):
    """User model with authentication and profile information"""
    __tablename__ = 'users'
    
    id = db.Column(db.Integer, primary_key=True)
    email = db.Column(db.String(120), unique=True, nullable=False, index=True)
    password_hash = db.Column(db.String(255), nullable=False)
    
    # Profile information
    first_name = db.Column(db.String(100), nullable=False)
    last_name = db.Column(db.String(100), nullable=False)
    phone = db.Column(db.String(20), nullable=True)
    
    # User status and roles
    is_active = db.Column(db.Boolean, default=True, nullable=False)
    is_admin = db.Column(db.<PERSON><PERSON><PERSON>, default=False, nullable=False)
    email_verified = db.Column(db.<PERSON>, default=False, nullable=False)
    
    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    last_login = db.Column(db.DateTime, nullable=True)
    
    # Relationships
    locations = db.relationship('UserLocation', back_populates='user', cascade='all, delete-orphan')
    vehicles = db.relationship('Vehicle', back_populates='user', cascade='all, delete-orphan')
    access_logs = db.relationship('AccessLog', back_populates='user', cascade='all, delete-orphan')
    
    def set_password(self, password):
        """Hash and set password"""
        self.password_hash = generate_password_hash(password)
    
    def check_password(self, password):
        """Check if provided password matches hash"""
        return check_password_hash(self.password_hash, password)
    
    @property
    def full_name(self):
        """Get user's full name"""
        return f"{self.first_name} {self.last_name}"
    
    def get_authorized_plates(self):
        """Get all license plates authorized for this user"""
        return [vehicle.license_plate.upper() for vehicle in self.vehicles if vehicle.is_active]
    
    def has_access_to_location(self, location_name):
        """Check if user has access to a specific location"""
        return any(loc.location_name == location_name and loc.is_active 
                  for loc in self.locations)
    
    def to_dict(self, include_sensitive=False):
        """Convert user to dictionary for API responses"""
        data = {
            'id': self.id,
            'email': self.email,
            'first_name': self.first_name,
            'last_name': self.last_name,
            'full_name': self.full_name,
            'phone': self.phone,
            'is_active': self.is_active,
            'is_admin': self.is_admin,
            'email_verified': self.email_verified,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'last_login': self.last_login.isoformat() if self.last_login else None,
            'locations': [loc.to_dict() for loc in self.locations],
            'vehicles': [vehicle.to_dict() for vehicle in self.vehicles]
        }
        
        if include_sensitive:
            data['password_hash'] = self.password_hash
            
        return data
    
    def __repr__(self):
        return f'<User {self.email}>'


class UserLocation(db.Model):
    """Locations that a user has access to"""
    __tablename__ = 'user_locations'
    
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    location_name = db.Column(db.String(100), nullable=False)
    location_address = db.Column(db.String(255), nullable=True)
    is_active = db.Column(db.Boolean, default=True, nullable=False)
    access_level = db.Column(db.String(20), default='resident', nullable=False)  # resident, guest, admin
    
    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    expires_at = db.Column(db.DateTime, nullable=True)  # For temporary access
    
    # Relationships
    user = db.relationship('User', back_populates='locations')
    
    def is_expired(self):
        """Check if location access has expired"""
        if self.expires_at is None:
            return False
        return datetime.utcnow() > self.expires_at
    
    def to_dict(self):
        """Convert location to dictionary"""
        return {
            'id': self.id,
            'location_name': self.location_name,
            'location_address': self.location_address,
            'is_active': self.is_active,
            'access_level': self.access_level,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'expires_at': self.expires_at.isoformat() if self.expires_at else None,
            'is_expired': self.is_expired()
        }
    
    def __repr__(self):
        return f'<UserLocation {self.user.email} -> {self.location_name}>'


class Vehicle(db.Model):
    """Vehicles owned by users"""
    __tablename__ = 'vehicles'
    
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    license_plate = db.Column(db.String(20), nullable=False, index=True)
    make = db.Column(db.String(50), nullable=True)
    model = db.Column(db.String(50), nullable=True)
    year = db.Column(db.Integer, nullable=True)
    color = db.Column(db.String(30), nullable=True)
    vehicle_type = db.Column(db.String(20), default='car', nullable=False)  # car, motorcycle, truck, etc.
    
    # Status
    is_active = db.Column(db.Boolean, default=True, nullable=False)
    is_temporary = db.Column(db.Boolean, default=False, nullable=False)
    
    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    expires_at = db.Column(db.DateTime, nullable=True)  # For temporary vehicles
    
    # Relationships
    user = db.relationship('User', back_populates='vehicles')
    access_logs = db.relationship('AccessLog', back_populates='vehicle', cascade='all, delete-orphan')
    
    def is_expired(self):
        """Check if vehicle registration has expired"""
        if self.expires_at is None:
            return False
        return datetime.utcnow() > self.expires_at
    
    def to_dict(self):
        """Convert vehicle to dictionary"""
        return {
            'id': self.id,
            'license_plate': self.license_plate,
            'make': self.make,
            'model': self.model,
            'year': self.year,
            'color': self.color,
            'vehicle_type': self.vehicle_type,
            'is_active': self.is_active,
            'is_temporary': self.is_temporary,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'expires_at': self.expires_at.isoformat() if self.expires_at else None,
            'is_expired': self.is_expired()
        }
    
    def __repr__(self):
        return f'<Vehicle {self.license_plate} ({self.user.email})>'


class AccessLog(db.Model):
    """Log of gate access attempts and successes"""
    __tablename__ = 'access_logs'
    
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=True)
    vehicle_id = db.Column(db.Integer, db.ForeignKey('vehicles.id'), nullable=True)
    
    # Access details
    license_plate = db.Column(db.String(20), nullable=True, index=True)
    location = db.Column(db.String(100), nullable=False)  # entrada, salida
    access_granted = db.Column(db.Boolean, nullable=False)
    access_method = db.Column(db.String(20), default='automatic', nullable=False)  # automatic, manual, api
    
    # Additional information
    image_path = db.Column(db.String(255), nullable=True)
    ip_address = db.Column(db.String(45), nullable=True)
    user_agent = db.Column(db.String(255), nullable=True)
    notes = db.Column(db.Text, nullable=True)
    
    # Timestamps
    timestamp = db.Column(db.DateTime, default=datetime.utcnow, nullable=False, index=True)
    
    # Relationships
    user = db.relationship('User', back_populates='access_logs')
    vehicle = db.relationship('Vehicle', back_populates='access_logs')
    
    def to_dict(self):
        """Convert access log to dictionary"""
        return {
            'id': self.id,
            'user_id': self.user_id,
            'vehicle_id': self.vehicle_id,
            'license_plate': self.license_plate,
            'location': self.location,
            'access_granted': self.access_granted,
            'access_method': self.access_method,
            'image_path': self.image_path,
            'ip_address': self.ip_address,
            'user_agent': self.user_agent,
            'notes': self.notes,
            'timestamp': self.timestamp.isoformat() if self.timestamp else None,
            'user': self.user.to_dict() if self.user else None,
            'vehicle': self.vehicle.to_dict() if self.vehicle else None
        }
    
    def __repr__(self):
        return f'<AccessLog {self.license_plate} @ {self.timestamp}>'


# Database utility functions
def init_db(app):
    """Initialize database with app context"""
    db.init_app(app)

    with app.app_context():
        # Import audit log model to ensure it's registered
        from src.security.audit_log import AuditLog

        db.create_all()
        
        # Create default admin user if none exists
        if not User.query.filter_by(is_admin=True).first():
            admin = User(
                email='<EMAIL>',
                first_name='Admin',
                last_name='User',
                is_admin=True,
                is_active=True,
                email_verified=True
            )
            admin.set_password('admin123')  # Change this in production!
            
            db.session.add(admin)
            db.session.commit()
            print("✅ Default admin user created: <EMAIL> / admin123")


def migrate_existing_plates():
    """Migrate existing authorized_license_plates.json to database"""
    import os
    from src.utils.config import config
    
    if not os.path.exists(config.AUTHORIZED_PLATES_FILE):
        return
    
    try:
        with open(config.AUTHORIZED_PLATES_FILE, 'r') as f:
            plates_data = json.load(f)
        
        # Create a default user for existing plates if needed
        default_user = User.query.filter_by(email='<EMAIL>').first()
        if not default_user:
            default_user = User(
                email='<EMAIL>',
                first_name='Legacy',
                last_name='User',
                is_active=True,
                email_verified=True
            )
            default_user.set_password('legacy123')  # Change this!
            db.session.add(default_user)
            db.session.flush()
        
        # Migrate plates
        for plate, info in plates_data.items():
            existing_vehicle = Vehicle.query.filter_by(license_plate=plate.upper()).first()
            if not existing_vehicle:
                vehicle = Vehicle(
                    user_id=default_user.id,
                    license_plate=plate.upper(),
                    make=info.get('make', 'Unknown'),
                    model=info.get('model', 'Unknown'),
                    is_active=True
                )
                db.session.add(vehicle)
        
        db.session.commit()
        print(f"✅ Migrated {len(plates_data)} plates to database")
        
    except Exception as e:
        print(f"❌ Error migrating plates: {e}")
        db.session.rollback()
