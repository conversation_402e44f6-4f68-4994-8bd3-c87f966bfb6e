"""
Enhanced Session Security for VecinoSeguro
Provides secure session management with advanced security features
"""

import os
import secrets
import hashlib
import time
from datetime import datetime, timedelta
from flask import session, request, current_app, g
from functools import wraps
from src.security.audit_log import AuditLogger

class SecureSessionManager:
    """Enhanced session management with security features"""
    
    def __init__(self, app=None):
        self.app = app
        self.session_timeout = 3600  # 1 hour default
        self.absolute_timeout = 86400  # 24 hours absolute max
        self.regenerate_interval = 1800  # 30 minutes
        self.max_sessions_per_user = 5
        self.active_sessions = {}
        
        if app:
            self.init_app(app)
    
    def init_app(self, app):
        """Initialize secure session management"""
        self.app = app
        
        # Configure secure session settings
        app.config.update({
            'SESSION_COOKIE_SECURE': False,  # Set to True in production with HTTPS
            'SESSION_COOKIE_HTTPONLY': True,  # No JavaScript access
            'SESSION_COOKIE_SAMESITE': 'Strict',  # CSRF protection
            'PERMANENT_SESSION_LIFETIME': timedelta(seconds=self.session_timeout),
            'SESSION_REFRESH_EACH_REQUEST': False,  # Manual refresh control
        })
        
        # Use secure session key if not set
        if not app.config.get('SECRET_KEY'):
            app.config['SECRET_KEY'] = secrets.token_urlsafe(32)
        
        # Register session handlers
        app.before_request(self._before_request)
        app.after_request(self._after_request)
    
    def _before_request(self):
        """Check session security before each request"""
        if not session:
            return
        
        # Check session timeout
        if self._is_session_expired():
            self.destroy_session('Session expired')
            return
        
        # Check for session hijacking
        if self._detect_session_hijacking():
            self.destroy_session('Session hijacking detected')
            return
        
        # Update session activity
        self._update_session_activity()
        
        # Regenerate session ID periodically
        if self._should_regenerate_session():
            self._regenerate_session_id()
    
    def _after_request(self, response):
        """Process session after request"""
        # Add security headers
        if session:
            response.headers['X-Session-Timeout'] = str(self.session_timeout)
        
        return response
    
    def create_session(self, user_id, user_agent=None, ip_address=None):
        """Create a new secure session"""
        # Limit sessions per user
        self._cleanup_user_sessions(user_id)
        
        # Generate session data
        session_id = secrets.token_urlsafe(32)
        session_data = {
            'user_id': user_id,
            'session_id': session_id,
            'created_at': time.time(),
            'last_activity': time.time(),
            'ip_address': ip_address or request.remote_addr,
            'user_agent_hash': self._hash_user_agent(user_agent or request.headers.get('User-Agent', '')),
            'regenerated_at': time.time(),
            'request_count': 0
        }
        
        # Store in Flask session
        session.permanent = True
        session.update(session_data)
        
        # Track active session
        if user_id not in self.active_sessions:
            self.active_sessions[user_id] = []
        
        self.active_sessions[user_id].append({
            'session_id': session_id,
            'created_at': time.time(),
            'ip_address': ip_address or request.remote_addr,
            'last_activity': time.time()
        })
        
        # Log session creation
        AuditLogger.log_system_event(
            'session_created',
            f"New session created for user {user_id}",
            additional_data={
                'user_id': user_id,
                'session_id': session_id,
                'ip_address': ip_address or request.remote_addr
            }
        )
        
        return session_id
    
    def destroy_session(self, reason='User logout'):
        """Destroy current session securely"""
        if not session:
            return
        
        user_id = session.get('user_id')
        session_id = session.get('session_id')
        
        # Log session destruction
        AuditLogger.log_system_event(
            'session_destroyed',
            f"Session destroyed: {reason}",
            additional_data={
                'user_id': user_id,
                'session_id': session_id,
                'reason': reason,
                'ip_address': request.remote_addr
            }
        )
        
        # Remove from active sessions
        if user_id and user_id in self.active_sessions:
            self.active_sessions[user_id] = [
                s for s in self.active_sessions[user_id] 
                if s['session_id'] != session_id
            ]
        
        # Clear session data
        session.clear()
    
    def _is_session_expired(self):
        """Check if session has expired"""
        if not session:
            return True
        
        created_at = session.get('created_at', 0)
        last_activity = session.get('last_activity', 0)
        current_time = time.time()
        
        # Check absolute timeout
        if current_time - created_at > self.absolute_timeout:
            return True
        
        # Check inactivity timeout
        if current_time - last_activity > self.session_timeout:
            return True
        
        return False
    
    def _detect_session_hijacking(self):
        """Detect potential session hijacking"""
        if not session:
            return False
        
        # Check IP address consistency
        session_ip = session.get('ip_address')
        current_ip = request.remote_addr
        
        if session_ip and session_ip != current_ip:
            # Log potential hijacking attempt
            AuditLogger.log_system_event(
                'session_hijacking_attempt',
                f"IP address mismatch detected",
                success=False,
                additional_data={
                    'session_ip': session_ip,
                    'current_ip': current_ip,
                    'user_id': session.get('user_id'),
                    'session_id': session.get('session_id')
                }
            )
            return True
        
        # Check User-Agent consistency
        session_ua_hash = session.get('user_agent_hash')
        current_ua_hash = self._hash_user_agent(request.headers.get('User-Agent', ''))
        
        if session_ua_hash and session_ua_hash != current_ua_hash:
            # Log potential hijacking attempt
            AuditLogger.log_system_event(
                'session_hijacking_attempt',
                f"User-Agent mismatch detected",
                success=False,
                additional_data={
                    'user_id': session.get('user_id'),
                    'session_id': session.get('session_id')
                }
            )
            return True
        
        return False
    
    def _update_session_activity(self):
        """Update session activity timestamp"""
        if session:
            session['last_activity'] = time.time()
            session['request_count'] = session.get('request_count', 0) + 1
            
            # Update active sessions tracking
            user_id = session.get('user_id')
            session_id = session.get('session_id')
            
            if user_id and user_id in self.active_sessions:
                for s in self.active_sessions[user_id]:
                    if s['session_id'] == session_id:
                        s['last_activity'] = time.time()
                        break
    
    def _should_regenerate_session(self):
        """Check if session ID should be regenerated"""
        if not session:
            return False
        
        regenerated_at = session.get('regenerated_at', 0)
        current_time = time.time()
        
        return current_time - regenerated_at > self.regenerate_interval
    
    def _regenerate_session_id(self):
        """Regenerate session ID for security"""
        if not session:
            return
        
        old_session_id = session.get('session_id')
        new_session_id = secrets.token_urlsafe(32)
        
        # Update session data
        session['session_id'] = new_session_id
        session['regenerated_at'] = time.time()
        
        # Update active sessions tracking
        user_id = session.get('user_id')
        if user_id and user_id in self.active_sessions:
            for s in self.active_sessions[user_id]:
                if s['session_id'] == old_session_id:
                    s['session_id'] = new_session_id
                    break
        
        # Log session regeneration
        AuditLogger.log_system_event(
            'session_regenerated',
            f"Session ID regenerated for security",
            additional_data={
                'user_id': user_id,
                'old_session_id': old_session_id,
                'new_session_id': new_session_id
            }
        )
    
    def _cleanup_user_sessions(self, user_id):
        """Clean up old sessions for a user"""
        if user_id not in self.active_sessions:
            return
        
        current_time = time.time()
        active_sessions = []
        
        # Keep only recent sessions
        for session_data in self.active_sessions[user_id]:
            if current_time - session_data['last_activity'] < self.absolute_timeout:
                active_sessions.append(session_data)
        
        # Limit number of sessions
        if len(active_sessions) >= self.max_sessions_per_user:
            # Keep only the most recent sessions
            active_sessions.sort(key=lambda x: x['last_activity'], reverse=True)
            active_sessions = active_sessions[:self.max_sessions_per_user-1]
        
        self.active_sessions[user_id] = active_sessions
    
    def _hash_user_agent(self, user_agent):
        """Create hash of user agent for comparison"""
        if not user_agent:
            return None
        
        return hashlib.sha256(user_agent.encode('utf-8')).hexdigest()
    
    def get_user_sessions(self, user_id):
        """Get active sessions for a user"""
        return self.active_sessions.get(user_id, [])
    
    def revoke_user_sessions(self, user_id, except_session_id=None):
        """Revoke all sessions for a user except optionally one"""
        if user_id in self.active_sessions:
            if except_session_id:
                self.active_sessions[user_id] = [
                    s for s in self.active_sessions[user_id] 
                    if s['session_id'] == except_session_id
                ]
            else:
                del self.active_sessions[user_id]
        
        # Log session revocation
        AuditLogger.log_system_event(
            'user_sessions_revoked',
            f"All sessions revoked for user {user_id}",
            additional_data={
                'user_id': user_id,
                'except_session_id': except_session_id
            }
        )

def require_fresh_session(max_age=300):
    """Decorator to require a fresh session (recent authentication)"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            if not session:
                return {'error': 'Authentication required'}, 401
            
            created_at = session.get('created_at', 0)
            current_time = time.time()
            
            if current_time - created_at > max_age:
                return {'error': 'Fresh authentication required'}, 401
            
            return f(*args, **kwargs)
        
        return decorated_function
    return decorator

def session_required(f):
    """Decorator to require valid session"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not session or not session.get('user_id'):
            return {'error': 'Valid session required'}, 401
        
        return f(*args, **kwargs)
    
    return decorated_function

# Global session manager instance
session_manager = SecureSessionManager()
