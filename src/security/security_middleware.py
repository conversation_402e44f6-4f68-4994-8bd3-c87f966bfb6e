"""
Security middleware for VecinoSeguro
Implements various security measures and monitoring
"""

import time
import hashlib
from datetime import datetime, timedelta
from collections import defaultdict
from flask import request, jsonify, current_app, g
from functools import wraps
from src.security.audit_log import AuditLogger

class SecurityMonitor:
    """Security monitoring and threat detection"""
    
    def __init__(self):
        # Rate limiting tracking
        self.request_counts = defaultdict(lambda: defaultdict(int))
        self.blocked_ips = {}
        self.suspicious_activities = defaultdict(list)
        
        # Failed login tracking
        self.failed_logins = defaultdict(list)
        self.locked_accounts = {}
        
        # Request fingerprinting
        self.request_fingerprints = defaultdict(list)
    
    def is_ip_blocked(self, ip_address):
        """Check if IP is currently blocked"""
        if ip_address in self.blocked_ips:
            block_until = self.blocked_ips[ip_address]
            if datetime.utcnow() < block_until:
                return True
            else:
                # Block expired, remove it
                del self.blocked_ips[ip_address]
        return False
    
    def block_ip(self, ip_address, duration_minutes=15):
        """Block an IP address for specified duration"""
        block_until = datetime.utcnow() + timedelta(minutes=duration_minutes)
        self.blocked_ips[ip_address] = block_until
        
        AuditLogger.log_system_event(
            'ip_blocked',
            f"IP {ip_address} blocked for {duration_minutes} minutes",
            additional_data={'ip_address': ip_address, 'duration_minutes': duration_minutes}
        )
    
    def track_failed_login(self, email, ip_address):
        """Track failed login attempts"""
        now = datetime.utcnow()
        
        # Clean old attempts (older than 1 hour)
        cutoff = now - timedelta(hours=1)
        self.failed_logins[email] = [
            attempt for attempt in self.failed_logins[email] 
            if attempt['timestamp'] > cutoff
        ]
        
        # Add new attempt
        self.failed_logins[email].append({
            'timestamp': now,
            'ip_address': ip_address
        })
        
        # Check if account should be locked
        if len(self.failed_logins[email]) >= 5:  # 5 failed attempts in 1 hour
            self.lock_account(email, 30)  # Lock for 30 minutes
            return True
        
        # Check if IP should be blocked
        ip_attempts = sum(1 for attempts in self.failed_logins.values() 
                         for attempt in attempts 
                         if attempt['ip_address'] == ip_address and 
                         attempt['timestamp'] > cutoff)
        
        if ip_attempts >= 10:  # 10 failed attempts from same IP in 1 hour
            self.block_ip(ip_address, 60)  # Block for 1 hour
        
        return False
    
    def lock_account(self, email, duration_minutes=30):
        """Lock an account for specified duration"""
        lock_until = datetime.utcnow() + timedelta(minutes=duration_minutes)
        self.locked_accounts[email] = lock_until
        
        AuditLogger.log_system_event(
            'account_locked',
            f"Account {email} locked for {duration_minutes} minutes due to failed login attempts",
            additional_data={'email': email, 'duration_minutes': duration_minutes}
        )
    
    def is_account_locked(self, email):
        """Check if account is currently locked"""
        if email in self.locked_accounts:
            lock_until = self.locked_accounts[email]
            if datetime.utcnow() < lock_until:
                return True
            else:
                # Lock expired, remove it
                del self.locked_accounts[email]
        return False
    
    def track_request(self, ip_address, endpoint, user_agent):
        """Track request patterns for anomaly detection"""
        now = datetime.utcnow()

        # Create request fingerprint
        fingerprint = hashlib.md5(f"{endpoint}:{user_agent}".encode()).hexdigest()

        # Clean old requests (older than 1 hour)
        cutoff = now - timedelta(hours=1)
        self.request_fingerprints[ip_address] = [
            req for req in self.request_fingerprints[ip_address]
            if req['timestamp'] > cutoff
        ]

        # Add new request
        self.request_fingerprints[ip_address].append({
            'timestamp': now,
            'endpoint': endpoint,
            'fingerprint': fingerprint,
            'user_agent': user_agent[:100]  # Truncate for storage
        })

        # Detect suspicious patterns
        recent_requests = self.request_fingerprints[ip_address]

        # Check for rapid requests (more than 1000 requests in 10 minutes for development)
        ten_minutes_ago = now - timedelta(minutes=10)
        recent_count = sum(1 for req in recent_requests if req['timestamp'] > ten_minutes_ago)

        if recent_count > 1000:  # Much higher limit for development
            self.add_suspicious_activity(ip_address, 'rapid_requests', {
                'count': recent_count,
                'timeframe': '10_minutes'
            })
            return 'rate_limit_exceeded'

        # Check for scanning behavior (accessing many different endpoints)
        unique_endpoints = len(set(req['endpoint'] for req in recent_requests))
        if unique_endpoints > 20:  # More than 20 different endpoints in 1 hour
            self.add_suspicious_activity(ip_address, 'endpoint_scanning', {
                'unique_endpoints': unique_endpoints
            })

        # Check for bot-like behavior patterns
        bot_indicators = self._detect_bot_behavior(recent_requests, user_agent)
        if bot_indicators:
            self.add_suspicious_activity(ip_address, 'bot_behavior', bot_indicators)

        # Check for SQL injection attempts
        if self._detect_sql_injection_attempt(endpoint):
            self.add_suspicious_activity(ip_address, 'sql_injection_attempt', {
                'endpoint': endpoint
            })
            return 'malicious_request'

        # Check for XSS attempts
        if self._detect_xss_attempt(endpoint):
            self.add_suspicious_activity(ip_address, 'xss_attempt', {
                'endpoint': endpoint
            })
            return 'malicious_request'

        return 'normal'

    def _detect_bot_behavior(self, recent_requests, user_agent):
        """Detect bot-like behavior patterns"""
        if not recent_requests:
            return None

        bot_indicators = {}

        # Check for suspicious user agents
        suspicious_agents = [
            'bot', 'crawler', 'spider', 'scraper', 'curl', 'wget',
            'python-requests', 'go-http-client', 'java/', 'okhttp'
        ]

        if any(agent.lower() in user_agent.lower() for agent in suspicious_agents):
            bot_indicators['suspicious_user_agent'] = user_agent[:100]

        # Check for too regular request intervals (bot-like timing)
        if len(recent_requests) >= 5:
            intervals = []
            for i in range(1, min(len(recent_requests), 10)):
                interval = (recent_requests[i]['timestamp'] - recent_requests[i-1]['timestamp']).total_seconds()
                intervals.append(interval)

            # If intervals are too regular (variance < 1 second), it's likely a bot
            if intervals and max(intervals) - min(intervals) < 1.0:
                bot_indicators['regular_intervals'] = {
                    'avg_interval': sum(intervals) / len(intervals),
                    'variance': max(intervals) - min(intervals)
                }

        # Check for identical request patterns
        fingerprints = [req['fingerprint'] for req in recent_requests[-10:]]
        if len(set(fingerprints)) == 1 and len(fingerprints) > 5:
            bot_indicators['identical_requests'] = len(fingerprints)

        return bot_indicators if bot_indicators else None

    def _detect_sql_injection_attempt(self, endpoint):
        """Detect potential SQL injection attempts"""
        if not endpoint:
            return False

        sql_patterns = [
            r"union\s+select", r"drop\s+table", r"insert\s+into", r"delete\s+from",
            r"update\s+set", r"exec\s*\(", r"execute\s*\(", r"sp_executesql",
            r"xp_cmdshell", r";\s*drop", r";\s*delete", r";\s*insert",
            r"'\s*or\s*'", r'"\s*or\s*"', r"'\s*and\s*'", r'"\s*and\s*"',
            r"'\s*=\s*'", r'"\s*=\s*"', r"--", r"/\*", r"\*/",
            r"char\s*\(", r"ascii\s*\(", r"substring\s*\(", r"waitfor\s+delay"
        ]

        import re
        endpoint_lower = endpoint.lower()

        for pattern in sql_patterns:
            if re.search(pattern, endpoint_lower, re.IGNORECASE):
                return True

        return False

    def _detect_xss_attempt(self, endpoint):
        """Detect potential XSS attempts"""
        if not endpoint:
            return False

        xss_patterns = [
            r"<script", r"</script>", r"javascript:", r"vbscript:", r"onload\s*=",
            r"onerror\s*=", r"onclick\s*=", r"onmouseover\s*=", r"onfocus\s*=",
            r"alert\s*\(", r"confirm\s*\(", r"prompt\s*\(", r"document\.cookie",
            r"document\.write", r"eval\s*\(", r"expression\s*\(", r"<iframe",
            r"<object", r"<embed", r"<applet", r"<meta", r"<link"
        ]

        import re
        endpoint_lower = endpoint.lower()

        for pattern in xss_patterns:
            if re.search(pattern, endpoint_lower, re.IGNORECASE):
                return True

        return False

    def add_suspicious_activity(self, ip_address, activity_type, details):
        """Record suspicious activity"""
        now = datetime.utcnow()
        
        self.suspicious_activities[ip_address].append({
            'timestamp': now,
            'type': activity_type,
            'details': details
        })
        
        AuditLogger.log_system_event(
            'suspicious_activity',
            f"Suspicious activity detected from {ip_address}: {activity_type}",
            additional_data={'ip_address': ip_address, 'activity_type': activity_type, 'details': details}
        )
        
        # Auto-block IPs with multiple suspicious activities
        recent_activities = [
            activity for activity in self.suspicious_activities[ip_address]
            if activity['timestamp'] > now - timedelta(hours=1)
        ]
        
        if len(recent_activities) >= 3:
            self.block_ip(ip_address, 120)  # Block for 2 hours

# Global security monitor instance
security_monitor = SecurityMonitor()

def security_middleware():
    """Security middleware to be applied to all requests"""
    ip_address = request.remote_addr
    endpoint = request.endpoint
    user_agent = request.headers.get('User-Agent', '')
    
    # Check if IP is blocked
    if security_monitor.is_ip_blocked(ip_address):
        AuditLogger.log_system_event(
            'blocked_ip_attempt',
            f"Blocked IP {ip_address} attempted to access {endpoint}",
            success=False,
            additional_data={'ip_address': ip_address, 'endpoint': endpoint}
        )
        return jsonify({'error': 'Acceso bloqueado temporalmente'}), 429
    
    # Track request patterns
    request_status = security_monitor.track_request(ip_address, endpoint, user_agent)
    
    if request_status == 'rate_limit_exceeded':
        return jsonify({'error': 'Demasiadas solicitudes. Intente más tarde.'}), 429
    
    # Store security info in request context
    g.security_info = {
        'ip_address': ip_address,
        'request_status': request_status,
        'timestamp': datetime.utcnow()
    }

def require_secure_headers(f):
    """Decorator to add security headers to responses"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        response = f(*args, **kwargs)
        
        # Add security headers
        if hasattr(response, 'headers'):
            response.headers['X-Content-Type-Options'] = 'nosniff'
            response.headers['X-Frame-Options'] = 'DENY'
            response.headers['X-XSS-Protection'] = '1; mode=block'
            response.headers['Strict-Transport-Security'] = 'max-age=********; includeSubDomains'
            response.headers['Content-Security-Policy'] = "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'"
        
        return response
    
    return decorated_function

def check_account_lockout(email):
    """Check if account is locked due to failed login attempts"""
    if security_monitor.is_account_locked(email):
        return jsonify({'error': 'Cuenta temporalmente bloqueada debido a múltiples intentos fallidos'}), 423
    return None

def record_failed_login(email, ip_address):
    """Record a failed login attempt"""
    account_locked = security_monitor.track_failed_login(email, ip_address)
    if account_locked:
        return jsonify({'error': 'Cuenta bloqueada debido a múltiples intentos fallidos'}), 423
    return None

def get_security_stats():
    """Get current security statistics"""
    now = datetime.utcnow()
    one_hour_ago = now - timedelta(hours=1)
    
    # Count recent activities
    recent_blocks = sum(1 for block_time in security_monitor.blocked_ips.values() 
                       if block_time > one_hour_ago)
    
    recent_locks = sum(1 for lock_time in security_monitor.locked_accounts.values() 
                      if lock_time > one_hour_ago)
    
    recent_suspicious = sum(
        len([activity for activity in activities 
             if activity['timestamp'] > one_hour_ago])
        for activities in security_monitor.suspicious_activities.values()
    )
    
    return {
        'blocked_ips': len(security_monitor.blocked_ips),
        'locked_accounts': len(security_monitor.locked_accounts),
        'recent_blocks': recent_blocks,
        'recent_locks': recent_locks,
        'recent_suspicious_activities': recent_suspicious,
        'total_suspicious_ips': len(security_monitor.suspicious_activities)
    }
