"""
Enhanced Security Headers for VecinoSeguro
Provides comprehensive HTTP security headers and Content Security Policy
"""

import secrets
from flask import request, current_app
from functools import wraps

class SecurityHeaders:
    """Enhanced security headers management"""
    
    def __init__(self, app=None):
        self.app = app
        self.nonce_cache = {}
        
        if app:
            self.init_app(app)
    
    def init_app(self, app):
        """Initialize security headers"""
        self.app = app
        
        # Register after_request handler
        app.after_request(self.add_security_headers)
    
    def generate_nonce(self):
        """Generate a cryptographic nonce for CSP"""
        return secrets.token_urlsafe(16)
    
    def add_security_headers(self, response):
        """Add comprehensive security headers to response"""

        # Generate nonce for this request
        nonce = self.generate_nonce()

        # Check if we're in development mode
        is_development = current_app.config.get('ENV') == 'development' or current_app.debug

        if is_development:
            # More permissive CSP for development - no nonce to allow unsafe-inline
            csp_directives = [
                "default-src 'self'",
                "script-src 'self' 'unsafe-inline' 'unsafe-eval'",
                "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://cdnjs.cloudflare.com",
                "img-src 'self' data: https:",
                "font-src 'self' data: https://fonts.gstatic.com https://cdnjs.cloudflare.com",
                "connect-src 'self'",
                "media-src 'self'",
                "object-src 'none'",
                "child-src 'none'",
                "frame-src 'none'",
                "worker-src 'none'",
                "manifest-src 'self'",
                "base-uri 'self'",
                "form-action 'self'",
                "frame-ancestors 'none'"
            ]
        else:
            # Stricter CSP for production
            csp_directives = [
                "default-src 'self'",
                f"script-src 'self' 'nonce-{nonce}' 'strict-dynamic'",
                f"style-src 'self' 'nonce-{nonce}'",
                "img-src 'self' data:",
                "font-src 'self' data:",
                "connect-src 'self'",
                "media-src 'self'",
                "object-src 'none'",
                "child-src 'none'",
                "frame-src 'none'",
                "worker-src 'none'",
                "manifest-src 'self'",
                "base-uri 'self'",
                "form-action 'self'",
                "frame-ancestors 'none'",
                "upgrade-insecure-requests"
            ]
        
        # Add report-uri if configured
        report_uri = current_app.config.get('CSP_REPORT_URI')
        if report_uri:
            csp_directives.append(f"report-uri {report_uri}")
        
        response.headers['Content-Security-Policy'] = '; '.join(csp_directives)

        # Store nonce for template use (only in production mode)
        if not is_development and hasattr(request, 'csp_nonce'):
            request.csp_nonce = nonce
        
        # X-Content-Type-Options
        response.headers['X-Content-Type-Options'] = 'nosniff'
        
        # X-Frame-Options
        response.headers['X-Frame-Options'] = 'DENY'
        
        # X-XSS-Protection (legacy browsers)
        response.headers['X-XSS-Protection'] = '1; mode=block'
        
        # Strict-Transport-Security (HSTS) - Only for HTTPS
        if request.is_secure:
            response.headers['Strict-Transport-Security'] = 'max-age=31536000; includeSubDomains; preload'
        elif not is_development:
            # In production without HTTPS, warn about security
            response.headers['X-Security-Warning'] = 'HTTPS recommended for production'
        
        # Referrer Policy
        response.headers['Referrer-Policy'] = 'strict-origin-when-cross-origin'
        
        # Permissions Policy (Feature Policy) - Only supported features
        permissions_policy = [
            "geolocation=()",
            "microphone=()",
            "camera=()",
            "payment=()",
            "usb=()",
            "magnetometer=()",
            "gyroscope=()",
            "fullscreen=(self)"
        ]
        response.headers['Permissions-Policy'] = ', '.join(permissions_policy)
        
        # Cross-Origin Policies
        response.headers['Cross-Origin-Embedder-Policy'] = 'require-corp'
        response.headers['Cross-Origin-Opener-Policy'] = 'same-origin'
        response.headers['Cross-Origin-Resource-Policy'] = 'same-origin'
        
        # Cache Control for sensitive pages
        if self._is_sensitive_endpoint(request.endpoint):
            response.headers['Cache-Control'] = 'no-cache, no-store, must-revalidate, private'
            response.headers['Pragma'] = 'no-cache'
            response.headers['Expires'] = '0'
        
        # Server header removal/modification
        response.headers['Server'] = 'VecinoSeguro'
        
        # Custom security headers
        response.headers['X-Permitted-Cross-Domain-Policies'] = 'none'
        response.headers['X-Download-Options'] = 'noopen'
        
        return response
    
    def _is_sensitive_endpoint(self, endpoint):
        """Check if endpoint contains sensitive data"""
        if not endpoint:
            return False
        
        sensitive_endpoints = [
            'auth.login', 'auth.register', 'auth.logout',
            'dashboard', 'profile', 'admin',
            'api.get_users', 'api.get_vehicles', 'api.get_logs'
        ]
        
        return endpoint in sensitive_endpoints or 'admin' in endpoint

def csp_nonce():
    """Template function to get CSP nonce"""
    return getattr(request, 'csp_nonce', '')

def secure_headers(additional_headers=None):
    """Decorator to add additional security headers to specific routes"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            response = f(*args, **kwargs)
            
            if additional_headers:
                for header, value in additional_headers.items():
                    response.headers[header] = value
            
            return response
        
        return decorated_function
    return decorator

class ContentSecurityPolicy:
    """Advanced Content Security Policy management"""
    
    def __init__(self):
        self.directives = {
            'default-src': ["'self'"],
            'script-src': ["'self'"],
            'style-src': ["'self'", "'unsafe-inline'"],
            'img-src': ["'self'", "data:", "https:"],
            'font-src': ["'self'", "data:"],
            'connect-src': ["'self'"],
            'media-src': ["'self'"],
            'object-src': ["'none'"],
            'child-src': ["'none'"],
            'frame-src': ["'none'"],
            'worker-src': ["'none'"],
            'manifest-src': ["'self'"],
            'base-uri': ["'self'"],
            'form-action': ["'self'"],
            'frame-ancestors': ["'none'"]
        }
        self.report_only = False
        self.report_uri = None
    
    def add_source(self, directive, source):
        """Add a source to a CSP directive"""
        if directive in self.directives:
            if source not in self.directives[directive]:
                self.directives[directive].append(source)
    
    def remove_source(self, directive, source):
        """Remove a source from a CSP directive"""
        if directive in self.directives and source in self.directives[directive]:
            self.directives[directive].remove(source)
    
    def set_report_uri(self, uri):
        """Set CSP report URI"""
        self.report_uri = uri
    
    def set_report_only(self, report_only=True):
        """Set CSP to report-only mode"""
        self.report_only = report_only
    
    def generate_policy(self, nonce=None):
        """Generate CSP policy string"""
        policy_parts = []
        
        for directive, sources in self.directives.items():
            if sources:
                # Add nonce to script-src and style-src if provided
                if nonce and directive in ['script-src', 'style-src']:
                    sources_with_nonce = sources + [f"'nonce-{nonce}'"]
                    policy_parts.append(f"{directive} {' '.join(sources_with_nonce)}")
                else:
                    policy_parts.append(f"{directive} {' '.join(sources)}")
        
        # Add upgrade-insecure-requests
        policy_parts.append("upgrade-insecure-requests")
        
        # Add report-uri if set
        if self.report_uri:
            policy_parts.append(f"report-uri {self.report_uri}")
        
        return '; '.join(policy_parts)
    
    def get_header_name(self):
        """Get appropriate CSP header name"""
        return 'Content-Security-Policy-Report-Only' if self.report_only else 'Content-Security-Policy'

class SecurityConfig:
    """Centralized security configuration"""
    
    def __init__(self, app=None):
        self.app = app
        self.config = {
            # Session security
            'SESSION_COOKIE_SECURE': True,
            'SESSION_COOKIE_HTTPONLY': True,
            'SESSION_COOKIE_SAMESITE': 'Strict',
            'PERMANENT_SESSION_LIFETIME': 3600,
            
            # CSRF protection
            'CSRF_ENABLED': True,
            'CSRF_TOKEN_LIFETIME': 3600,
            'CSRF_HEADER_NAME': 'X-CSRF-Token',
            
            # Rate limiting
            'RATELIMIT_ENABLED': True,
            'RATELIMIT_DEFAULT': '100 per hour',
            'RATELIMIT_STORAGE_URL': 'memory://',
            
            # Security headers
            'SECURITY_HEADERS_ENABLED': True,
            'CSP_ENABLED': True,
            'CSP_REPORT_ONLY': False,
            'CSP_REPORT_URI': None,
            
            # Input validation
            'STRICT_VALIDATION': True,
            'MAX_CONTENT_LENGTH': 16 * 1024 * 1024,  # 16MB
            
            # Audit logging
            'AUDIT_ENABLED': True,
            'AUDIT_LOG_REQUESTS': True,
            'AUDIT_LOG_RESPONSES': False,
            
            # Threat detection
            'THREAT_DETECTION_ENABLED': True,
            'AUTO_BLOCK_ENABLED': True,
            'BLOCK_DURATION': 3600,  # 1 hour
        }
        
        if app:
            self.init_app(app)
    
    def init_app(self, app):
        """Initialize security configuration"""
        self.app = app
        
        # Apply security configuration
        for key, value in self.config.items():
            app.config.setdefault(key, value)
        
        # Initialize security components
        if app.config.get('SECURITY_HEADERS_ENABLED'):
            SecurityHeaders(app)
    
    def update_config(self, **kwargs):
        """Update security configuration"""
        self.config.update(kwargs)
        
        if self.app:
            for key, value in kwargs.items():
                self.app.config[key] = value
    
    def get_config(self, key, default=None):
        """Get security configuration value"""
        return self.config.get(key, default)

# Global security configuration
security_config = SecurityConfig()
