"""
Enhanced CSRF Protection for VecinoSeguro
Provides comprehensive Cross-Site Request Forgery protection
"""

import secrets
import hmac
import hashlib
import time
from datetime import datetime, timedelta
from flask import request, session, jsonify, current_app, g
from functools import wraps
from src.security.audit_log import AuditLogger

class CSRFProtection:
    """Enhanced CSRF protection with token validation and monitoring"""
    
    def __init__(self, app=None):
        self.app = app
        self.secret_key = None
        self.token_lifetime = 3600  # 1 hour
        self.header_name = 'X-CSRF-Token'
        self.field_name = 'csrf_token'
        self.exempt_views = set()
        
        if app:
            self.init_app(app)
    
    def init_app(self, app):
        """Initialize CSRF protection with Flask app"""
        self.app = app
        self.secret_key = app.config.get('SECRET_KEY', '').encode('utf-8')
        self.token_lifetime = app.config.get('CSRF_TOKEN_LIFETIME', 3600)
        
        # Register before_request handler
        app.before_request(self._check_csrf_token)
        
        # Add template global for CSRF token
        app.jinja_env.globals['csrf_token'] = self.generate_csrf_token
    
    def generate_csrf_token(self):
        """Generate a new CSRF token"""
        if 'csrf_token' not in session:
            session['csrf_token'] = secrets.token_urlsafe(32)
            session['csrf_token_time'] = time.time()
        
        return session['csrf_token']
    
    def validate_csrf_token(self, token=None):
        """Validate CSRF token"""
        if not token:
            # Try to get token from header or form data
            token = request.headers.get(self.header_name)
            if not token:
                token = request.form.get(self.field_name)
            if not token:
                token = request.json.get(self.field_name) if request.is_json else None
        
        if not token:
            return False, "CSRF token missing"
        
        # Check if token exists in session
        session_token = session.get('csrf_token')
        if not session_token:
            return False, "No CSRF token in session"
        
        # Check token age
        token_time = session.get('csrf_token_time', 0)
        if time.time() - token_time > self.token_lifetime:
            return False, "CSRF token expired"
        
        # Validate token
        if not hmac.compare_digest(token, session_token):
            return False, "CSRF token invalid"
        
        return True, "CSRF token valid"
    
    def _check_csrf_token(self):
        """Check CSRF token for state-changing requests"""
        # Skip for safe methods
        if request.method in ['GET', 'HEAD', 'OPTIONS']:
            return
        
        # Skip for exempt views
        if request.endpoint in self.exempt_views:
            return
        
        # Skip for all API endpoints (they use JWT authentication)
        if request.path.startswith('/api/'):
            return
        
        # Skip for auth endpoints (they have their own protection)
        if request.path.startswith('/auth/'):
            return
        
        # Validate CSRF token
        valid, reason = self.validate_csrf_token()
        
        if not valid:
            # Log CSRF attack attempt
            AuditLogger.log_system_event(
                'csrf_attack_attempt',
                f"CSRF attack attempt from {request.remote_addr}: {reason}",
                success=False,
                additional_data={
                    'ip_address': request.remote_addr,
                    'user_agent': request.headers.get('User-Agent', ''),
                    'endpoint': request.endpoint,
                    'method': request.method,
                    'reason': reason
                }
            )
            
            if request.is_json:
                return jsonify({'error': 'CSRF token validation failed'}), 403
            else:
                return "CSRF token validation failed", 403
    
    def exempt(self, view):
        """Decorator to exempt a view from CSRF protection"""
        if isinstance(view, str):
            self.exempt_views.add(view)
            return view
        else:
            self.exempt_views.add(view.__name__)
            return view

def csrf_protect(f):
    """Decorator to explicitly require CSRF protection"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        csrf = current_app.extensions.get('csrf')
        if csrf:
            valid, reason = csrf.validate_csrf_token()
            if not valid:
                AuditLogger.log_system_event(
                    'csrf_protection_triggered',
                    f"CSRF protection triggered for {request.endpoint}: {reason}",
                    success=False
                )
                
                if request.is_json:
                    return jsonify({'error': 'CSRF protection failed'}), 403
                else:
                    return "CSRF protection failed", 403
        
        return f(*args, **kwargs)
    
    return decorated_function

def generate_csrf_meta_tag():
    """Generate HTML meta tag for CSRF token"""
    csrf = current_app.extensions.get('csrf')
    if csrf:
        token = csrf.generate_csrf_token()
        return f'<meta name="csrf-token" content="{token}">'
    return ''

class DoubleSubmitCSRF:
    """Double Submit Cookie CSRF protection for API endpoints"""
    
    def __init__(self, app=None):
        self.app = app
        self.cookie_name = 'csrf_token'
        self.header_name = 'X-CSRF-Token'
        
        if app:
            self.init_app(app)
    
    def init_app(self, app):
        """Initialize double submit CSRF protection"""
        self.app = app
        self.cookie_name = app.config.get('CSRF_COOKIE_NAME', 'csrf_token')
        self.header_name = app.config.get('CSRF_HEADER_NAME', 'X-CSRF-Token')
    
    def set_csrf_cookie(self, response):
        """Set CSRF token in cookie"""
        token = secrets.token_urlsafe(32)
        response.set_cookie(
            self.cookie_name,
            token,
            max_age=3600,
            secure=True,
            httponly=False,  # Needs to be accessible to JavaScript
            samesite='Strict'
        )
        return token
    
    def validate_double_submit(self):
        """Validate double submit CSRF token"""
        cookie_token = request.cookies.get(self.cookie_name)
        header_token = request.headers.get(self.header_name)
        
        if not cookie_token or not header_token:
            return False, "CSRF tokens missing"
        
        if not hmac.compare_digest(cookie_token, header_token):
            return False, "CSRF tokens do not match"
        
        return True, "CSRF tokens valid"

def double_submit_csrf_protect(f):
    """Decorator for double submit CSRF protection"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        # Skip for safe methods
        if request.method in ['GET', 'HEAD', 'OPTIONS']:
            return f(*args, **kwargs)
        
        csrf = DoubleSubmitCSRF()
        valid, reason = csrf.validate_double_submit()
        
        if not valid:
            AuditLogger.log_system_event(
                'double_submit_csrf_failed',
                f"Double submit CSRF validation failed: {reason}",
                success=False,
                additional_data={
                    'endpoint': request.endpoint,
                    'method': request.method,
                    'ip_address': request.remote_addr
                }
            )
            
            return jsonify({'error': 'CSRF validation failed'}), 403
        
        return f(*args, **kwargs)
    
    return decorated_function

class CSRFTokenManager:
    """Advanced CSRF token management with rotation and validation"""
    
    def __init__(self):
        self.tokens = {}  # Store tokens with metadata
        self.max_tokens_per_session = 5
        self.cleanup_interval = 300  # 5 minutes
        self.last_cleanup = time.time()
    
    def generate_token(self, session_id):
        """Generate a new CSRF token for a session"""
        token = secrets.token_urlsafe(32)
        timestamp = time.time()
        
        # Initialize session tokens if not exists
        if session_id not in self.tokens:
            self.tokens[session_id] = []
        
        # Add new token
        self.tokens[session_id].append({
            'token': token,
            'created': timestamp,
            'used': False
        })
        
        # Limit number of tokens per session
        if len(self.tokens[session_id]) > self.max_tokens_per_session:
            self.tokens[session_id] = self.tokens[session_id][-self.max_tokens_per_session:]
        
        # Cleanup old tokens periodically
        if time.time() - self.last_cleanup > self.cleanup_interval:
            self._cleanup_expired_tokens()
        
        return token
    
    def validate_token(self, session_id, token, mark_used=True):
        """Validate and optionally mark token as used"""
        if session_id not in self.tokens:
            return False, "No tokens for session"
        
        session_tokens = self.tokens[session_id]
        
        for token_data in session_tokens:
            if hmac.compare_digest(token_data['token'], token):
                # Check if token is expired (1 hour)
                if time.time() - token_data['created'] > 3600:
                    return False, "Token expired"
                
                # Check if token was already used (optional one-time use)
                if token_data['used']:
                    return False, "Token already used"
                
                # Mark as used if requested
                if mark_used:
                    token_data['used'] = True
                
                return True, "Token valid"
        
        return False, "Token not found"
    
    def _cleanup_expired_tokens(self):
        """Remove expired tokens"""
        current_time = time.time()
        
        for session_id in list(self.tokens.keys()):
            # Remove expired tokens
            self.tokens[session_id] = [
                token_data for token_data in self.tokens[session_id]
                if current_time - token_data['created'] <= 3600
            ]
            
            # Remove empty sessions
            if not self.tokens[session_id]:
                del self.tokens[session_id]
        
        self.last_cleanup = current_time

# Global CSRF token manager
csrf_token_manager = CSRFTokenManager()
