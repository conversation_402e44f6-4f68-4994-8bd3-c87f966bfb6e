"""
Unified Authorization Service for VecinoSeguro
Handles license plate authorization for both web and gate control systems
"""

import json
import os
from datetime import datetime
from typing import Tuple, Optional, Dict, Any
from dataclasses import dataclass

from src.models.models import db, User, Vehicle, UserLocation, AccessLog
from src.utils.config import config
from src.security.audit_log import AuditLogger

@dataclass
class AuthorizationResult:
    """Result of an authorization check"""
    authorized: bool
    user_info: Optional[Dict[str, Any]] = None
    vehicle_info: Optional[Dict[str, Any]] = None
    location_access: Optional[Dict[str, Any]] = None
    source: str = 'unknown'
    reason: str = ''

class AuthorizationService:
    """Unified service for license plate authorization"""
    
    def __init__(self):
        self.json_plates_cache = {}
        self.cache_timestamp = None
        self.cache_ttl = 300  # 5 minutes cache for JSON file
    
    def _load_json_plates(self) -> Dict[str, Any]:
        """Load plates from JSON file with caching"""
        try:
            # Check if cache is still valid
            if (self.cache_timestamp and 
                (datetime.now() - self.cache_timestamp).seconds < self.cache_ttl):
                return self.json_plates_cache
            
            # Load from file
            if os.path.exists(config.AUTHORIZED_PLATES_FILE):
                with open(config.AUTHORIZED_PLATES_FILE, 'r') as f:
                    self.json_plates_cache = json.load(f)
                    self.cache_timestamp = datetime.now()
            else:
                self.json_plates_cache = {}
                
            return self.json_plates_cache
            
        except Exception as e:
            print(f"Error loading JSON plates: {e}")
            return {}
    
    def check_plate_authorization(self, license_plate: str, location: str = None) -> AuthorizationResult:
        """
        Check if a license plate is authorized for access
        
        Args:
            license_plate: The license plate to check
            location: The location where access is requested (entrada/salida)
            
        Returns:
            AuthorizationResult with authorization details
        """
        if not license_plate:
            return AuthorizationResult(
                authorized=False,
                reason="No license plate provided"
            )
        
        plate = license_plate.upper().strip()
        
        # First, check database (primary source)
        db_result = self._check_database_authorization(plate, location)
        if db_result.authorized:
            return db_result
        
        # Fallback to JSON file (backward compatibility)
        json_result = self._check_json_authorization(plate, location)
        if json_result.authorized:
            return json_result
        
        # Not authorized in either source
        return AuthorizationResult(
            authorized=False,
            reason=f"License plate {plate} not found in authorized vehicles",
            source="none"
        )
    
    def _check_database_authorization(self, plate: str, location: str = None) -> AuthorizationResult:
        """Check authorization against database"""
        try:
            # Find active vehicle with this plate
            vehicle = Vehicle.query.filter_by(
                license_plate=plate,
                is_active=True
            ).first()
            
            if not vehicle:
                return AuthorizationResult(
                    authorized=False,
                    reason=f"Vehicle with plate {plate} not found in database",
                    source="database"
                )
            
            # Check if vehicle is expired
            if vehicle.is_expired():
                return AuthorizationResult(
                    authorized=False,
                    reason=f"Vehicle {plate} registration has expired",
                    source="database",
                    vehicle_info=vehicle.to_dict()
                )
            
            # Check if user is active
            if not vehicle.user.is_active:
                return AuthorizationResult(
                    authorized=False,
                    reason=f"User account for vehicle {plate} is deactivated",
                    source="database",
                    user_info=vehicle.user.to_dict(),
                    vehicle_info=vehicle.to_dict()
                )
            
            # Check location access if specified
            location_access_info = None
            if location:
                location_access_info = self._check_location_access(vehicle.user, location)
                if not location_access_info['has_access']:
                    return AuthorizationResult(
                        authorized=False,
                        reason=f"User does not have access to location: {location}",
                        source="database",
                        user_info=vehicle.user.to_dict(),
                        vehicle_info=vehicle.to_dict(),
                        location_access=location_access_info
                    )
            
            # All checks passed - authorized
            return AuthorizationResult(
                authorized=True,
                reason=f"Vehicle {plate} authorized for user {vehicle.user.full_name}",
                source="database",
                user_info=vehicle.user.to_dict(),
                vehicle_info=vehicle.to_dict(),
                location_access=location_access_info
            )
            
        except Exception as e:
            print(f"Error checking database authorization: {e}")
            return AuthorizationResult(
                authorized=False,
                reason=f"Database error: {str(e)}",
                source="database"
            )
    
    def _check_json_authorization(self, plate: str, location: str = None) -> AuthorizationResult:
        """Check authorization against JSON file (backward compatibility)"""
        try:
            json_plates = self._load_json_plates()
            
            if plate in json_plates:
                plate_info = json_plates[plate]
                return AuthorizationResult(
                    authorized=True,
                    reason=f"Vehicle {plate} found in legacy authorization file",
                    source="json_file",
                    user_info={
                        'name': plate_info.get('name', 'Unknown'),
                        'casa': plate_info.get('casa', 'Unknown'),
                        'source': 'json_file'
                    },
                    vehicle_info={
                        'license_plate': plate,
                        'vehiculo': plate_info.get('vehiculo', 'Unknown'),
                        'source': 'json_file'
                    }
                )
            
            return AuthorizationResult(
                authorized=False,
                reason=f"Vehicle {plate} not found in legacy authorization file",
                source="json_file"
            )
            
        except Exception as e:
            print(f"Error checking JSON authorization: {e}")
            return AuthorizationResult(
                authorized=False,
                reason=f"JSON file error: {str(e)}",
                source="json_file"
            )
    
    def _check_location_access(self, user: User, location: str) -> Dict[str, Any]:
        """Check if user has access to specific location"""
        try:
            # Admin users have access to all locations
            if user.is_admin:
                return {
                    'has_access': True,
                    'access_level': 'admin',
                    'reason': 'Admin user has access to all locations'
                }
            
            # Check user's assigned locations
            for user_location in user.locations:
                if (user_location.is_active and 
                    not user_location.is_expired() and
                    (user_location.location_name.lower() == location.lower() or
                     location.lower() in ['entrada', 'salida'])):  # Gate locations are generally accessible
                    
                    return {
                        'has_access': True,
                        'access_level': user_location.access_level,
                        'location_name': user_location.location_name,
                        'expires_at': user_location.expires_at.isoformat() if user_location.expires_at else None,
                        'reason': f'User has {user_location.access_level} access to {user_location.location_name}'
                    }
            
            # For gate access (entrada/salida), if user has any active location, allow access
            if location.lower() in ['entrada', 'salida'] and user.locations:
                active_locations = [loc for loc in user.locations if loc.is_active and not loc.is_expired()]
                if active_locations:
                    return {
                        'has_access': True,
                        'access_level': 'resident',
                        'reason': f'User has access through {len(active_locations)} active location(s)'
                    }
            
            return {
                'has_access': False,
                'reason': f'User does not have access to location: {location}'
            }
            
        except Exception as e:
            print(f"Error checking location access: {e}")
            return {
                'has_access': False,
                'reason': f'Error checking location access: {str(e)}'
            }
    
    def log_access_attempt(self, license_plate: str, location: str, 
                          authorization_result: AuthorizationResult, 
                          image_path: str = None, 
                          ip_address: str = 'system') -> None:
        """Log an access attempt with full context"""
        try:
            # Determine user and vehicle from authorization result
            user = None
            vehicle = None
            
            if authorization_result.user_info and authorization_result.source == 'database':
                try:
                    user_id = authorization_result.user_info.get('id')
                    if user_id:
                        user = User.query.get(user_id)
                except Exception as e:
                    print(f"Error getting user: {e}")
                    pass
            
            if authorization_result.vehicle_info and authorization_result.source == 'database':
                try:
                    vehicle_id = authorization_result.vehicle_info.get('id')
                    if vehicle_id:
                        vehicle = Vehicle.query.get(vehicle_id)
                except Exception as e:
                    print(f"Error getting vehicle: {e}")
                    pass
            
            # Create access log entry
            access_log = AccessLog(
                user_id=user.id if user else None,
                vehicle_id=vehicle.id if vehicle else None,
                license_plate=license_plate.upper() if license_plate else None,
                location=location,
                access_granted=authorization_result.authorized,
                access_method='automatic',
                image_path=image_path,
                ip_address=ip_address,
                notes=authorization_result.reason
            )
            
            db.session.add(access_log)
            db.session.commit()
            
            # Also log to audit system
            AuditLogger.log_access_control(
                'gate_access_attempt',
                user,
                f"Gate access attempt: {license_plate} at {location}",
                success=authorization_result.authorized,
                error_message=None if authorization_result.authorized else authorization_result.reason,
                additional_data={
                    'license_plate': license_plate,
                    'location': location,
                    'source': authorization_result.source,
                    'image_path': image_path,
                    'user_info': authorization_result.user_info,
                    'vehicle_info': authorization_result.vehicle_info
                }
            )
            
            print(f"Access logged: {license_plate} at {location} - {'GRANTED' if authorization_result.authorized else 'DENIED'}")
            
        except Exception as e:
            print(f"Error logging access attempt: {e}")
            # Fallback to JSON logging if database fails
            self._log_to_json_file(license_plate, location, authorization_result, image_path, ip_address)
    
    def _log_to_json_file(self, license_plate: str, location: str, 
                         authorization_result: AuthorizationResult, 
                         image_path: str = None, 
                         ip_address: str = 'system') -> None:
        """Fallback JSON file logging"""
        try:
            relative_image_path = None
            if image_path:
                relative_image_path = os.path.relpath(image_path, config.SNAPSHOTS_DIR)
            
            event = {
                "timestamp": datetime.utcnow().isoformat(),
                "ubicacion": location,
                "placa": license_plate,
                "autorizado": authorization_result.authorized,
                "detalles": authorization_result.reason,
                "imagen": relative_image_path,
                "ip_origen": ip_address,
                "source": authorization_result.source,
                "user_info": authorization_result.user_info,
                "vehicle_info": authorization_result.vehicle_info
            }
            
            # Create logs directory if it doesn't exist
            os.makedirs(config.LOGS_DIR, exist_ok=True)
            
            filename = f"{config.LOGS_DIR}/event_{location}_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}.json"
            with open(filename, "w") as f:
                json.dump(event, f, indent=2)
            
            print(f"📝 Event logged to JSON: {filename}")
        
        except Exception as e:
            print(f"❌ Error logging to JSON file: {e}")
    
    def get_all_authorized_plates(self) -> Dict[str, Dict[str, Any]]:
        """Get all authorized plates from both database and JSON file"""
        plates = {}
        
        # Get plates from database
        try:
            vehicles = Vehicle.query.filter_by(is_active=True).all()
            for vehicle in vehicles:
                if not vehicle.is_expired():
                    plates[vehicle.license_plate] = {
                        'name': vehicle.user.full_name,
                        'user_id': vehicle.user_id,
                        'vehicle_id': vehicle.id,
                        'make': vehicle.make,
                        'model': vehicle.model,
                        'year': vehicle.year,
                        'color': vehicle.color,
                        'source': 'database',
                        'is_temporary': vehicle.is_temporary,
                        'expires_at': vehicle.expires_at.isoformat() if vehicle.expires_at else None
                    }
        except Exception as e:
            print(f"Error loading plates from database: {e}")
        
        # Merge with JSON file plates (backward compatibility)
        try:
            json_plates = self._load_json_plates()
            for plate, info in json_plates.items():
                if plate not in plates:  # Don't override database entries
                    plates[plate] = {
                        'name': info.get('name', 'Unknown'),
                        'casa': info.get('casa', 'Unknown'),
                        'vehiculo': info.get('vehiculo', 'Unknown'),
                        'source': 'json_file',
                        'added_date': info.get('added_date')
                    }
        except Exception as e:
            print(f"Error loading plates from JSON file: {e}")
        
        return plates

# Global authorization service instance
authorization_service = AuthorizationService()
