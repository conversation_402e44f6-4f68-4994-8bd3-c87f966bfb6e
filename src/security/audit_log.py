"""
Audit logging system for VecinoSeguro
Tracks all security-relevant actions and changes
"""

import json
from datetime import datetime
from flask import request, current_app
from src.models.models import db
from sqlalchemy import Column, Integer, String, DateTime, Text, Boolean

class AuditLog(db.Model):
    """Audit log model for tracking security events"""
    __tablename__ = 'audit_logs'
    
    id = Column(Integer, primary_key=True)
    
    # Event information
    event_type = Column(String(50), nullable=False, index=True)  # login, logout, create, update, delete, etc.
    event_category = Column(String(30), nullable=False, index=True)  # auth, user, vehicle, location, system
    event_description = Column(String(255), nullable=False)
    
    # User information
    user_id = Column(Integer, nullable=True, index=True)
    user_email = Column(String(120), nullable=True)
    user_role = Column(String(20), nullable=True)  # admin, user
    
    # Request information
    ip_address = Column(String(45), nullable=True, index=True)
    user_agent = Column(String(500), nullable=True)
    endpoint = Column(String(100), nullable=True)
    method = Column(String(10), nullable=True)
    
    # Resource information
    resource_type = Column(String(50), nullable=True)  # user, vehicle, location, etc.
    resource_id = Column(String(50), nullable=True)
    resource_name = Column(String(100), nullable=True)
    
    # Event details
    old_values = Column(Text, nullable=True)  # JSON string of old values
    new_values = Column(Text, nullable=True)  # JSON string of new values
    additional_data = Column(Text, nullable=True)  # JSON string for extra data
    
    # Status
    success = Column(Boolean, nullable=False, default=True)
    error_message = Column(String(500), nullable=True)
    
    # Timestamp
    timestamp = Column(DateTime, default=datetime.utcnow, nullable=False, index=True)
    
    def to_dict(self):
        """Convert audit log to dictionary"""
        return {
            'id': self.id,
            'event_type': self.event_type,
            'event_category': self.event_category,
            'event_description': self.event_description,
            'user_id': self.user_id,
            'user_email': self.user_email,
            'user_role': self.user_role,
            'ip_address': self.ip_address,
            'user_agent': self.user_agent,
            'endpoint': self.endpoint,
            'method': self.method,
            'resource_type': self.resource_type,
            'resource_id': self.resource_id,
            'resource_name': self.resource_name,
            'old_values': json.loads(self.old_values) if self.old_values else None,
            'new_values': json.loads(self.new_values) if self.new_values else None,
            'additional_data': json.loads(self.additional_data) if self.additional_data else None,
            'success': self.success,
            'error_message': self.error_message,
            'timestamp': self.timestamp.isoformat() if self.timestamp else None
        }

class AuditLogger:
    """Audit logging utility class"""
    
    @staticmethod
    def get_request_info():
        """Extract request information"""
        return {
            'ip_address': request.remote_addr,
            'user_agent': request.headers.get('User-Agent', '')[:500],
            'endpoint': request.endpoint,
            'method': request.method
        }
    
    @staticmethod
    def get_user_info(user=None):
        """Extract user information"""
        if not user:
            # Try to get user from request context
            if hasattr(request, 'current_user'):
                user = request.current_user
            else:
                return {'user_id': None, 'user_email': None, 'user_role': None}
        
        return {
            'user_id': user.id if user else None,
            'user_email': user.email if user else None,
            'user_role': 'admin' if user and user.is_admin else 'user' if user else None
        }
    
    @staticmethod
    def log_event(event_type, event_category, description, user=None, resource_type=None, 
                  resource_id=None, resource_name=None, old_values=None, new_values=None, 
                  additional_data=None, success=True, error_message=None):
        """Log an audit event"""
        try:
            # Get request and user info
            request_info = AuditLogger.get_request_info()
            user_info = AuditLogger.get_user_info(user)
            
            # Create audit log entry
            audit_log = AuditLog(
                event_type=event_type,
                event_category=event_category,
                event_description=description,
                **user_info,
                **request_info,
                resource_type=resource_type,
                resource_id=str(resource_id) if resource_id else None,
                resource_name=resource_name,
                old_values=json.dumps(old_values) if old_values else None,
                new_values=json.dumps(new_values) if new_values else None,
                additional_data=json.dumps(additional_data) if additional_data else None,
                success=success,
                error_message=error_message
            )
            
            db.session.add(audit_log)
            db.session.commit()
            
        except Exception as e:
            # Don't let audit logging break the main functionality
            current_app.logger.error(f"Failed to log audit event: {str(e)}")
            try:
                db.session.rollback()
            except:
                pass
    
    @staticmethod
    def log_authentication(event_type, user_email, success=True, error_message=None, additional_data=None):
        """Log authentication events"""
        AuditLogger.log_event(
            event_type=event_type,
            event_category='auth',
            description=f"User {event_type}: {user_email}",
            resource_type='user',
            resource_name=user_email,
            success=success,
            error_message=error_message,
            additional_data=additional_data
        )
    
    @staticmethod
    def log_user_action(event_type, user, description, resource_type=None, resource_id=None, 
                       resource_name=None, old_values=None, new_values=None):
        """Log user actions"""
        AuditLogger.log_event(
            event_type=event_type,
            event_category='user',
            description=description,
            user=user,
            resource_type=resource_type,
            resource_id=resource_id,
            resource_name=resource_name,
            old_values=old_values,
            new_values=new_values
        )
    
    @staticmethod
    def log_vehicle_action(event_type, user, vehicle, description, old_values=None, new_values=None):
        """Log vehicle-related actions"""
        AuditLogger.log_event(
            event_type=event_type,
            event_category='vehicle',
            description=description,
            user=user,
            resource_type='vehicle',
            resource_id=vehicle.id if vehicle else None,
            resource_name=vehicle.license_plate if vehicle else None,
            old_values=old_values,
            new_values=new_values
        )
    
    @staticmethod
    def log_location_action(event_type, user, location, description, old_values=None, new_values=None):
        """Log location-related actions"""
        AuditLogger.log_event(
            event_type=event_type,
            event_category='location',
            description=description,
            user=user,
            resource_type='location',
            resource_id=location.id if location else None,
            resource_name=location.location_name if location else None,
            old_values=old_values,
            new_values=new_values
        )
    
    @staticmethod
    def log_access_control(event_type, user, description, success=True, error_message=None, additional_data=None):
        """Log access control events"""
        AuditLogger.log_event(
            event_type=event_type,
            event_category='access_control',
            description=description,
            user=user,
            success=success,
            error_message=error_message,
            additional_data=additional_data
        )
    
    @staticmethod
    def log_system_event(event_type, description, success=True, error_message=None, additional_data=None):
        """Log system events"""
        AuditLogger.log_event(
            event_type=event_type,
            event_category='system',
            description=description,
            success=success,
            error_message=error_message,
            additional_data=additional_data
        )

def audit_required(event_type, event_category, description_template=None):
    """Decorator to automatically audit function calls"""
    from functools import wraps

    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            start_time = datetime.utcnow()
            success = True
            error_message = None
            result = None
            
            try:
                result = f(*args, **kwargs)
                return result
            except Exception as e:
                success = False
                error_message = str(e)
                raise
            finally:
                # Log the event
                try:
                    description = description_template or f"{f.__name__} called"
                    if hasattr(request, 'current_user') and request.current_user:
                        user = request.current_user
                    else:
                        user = None
                    
                    additional_data = {
                        'function': f.__name__,
                        'duration_ms': int((datetime.utcnow() - start_time).total_seconds() * 1000)
                    }
                    
                    AuditLogger.log_event(
                        event_type=event_type,
                        event_category=event_category,
                        description=description,
                        user=user,
                        success=success,
                        error_message=error_message,
                        additional_data=additional_data
                    )
                except:
                    # Don't let audit logging break the main functionality
                    pass
        
        return decorated_function
    return decorator
