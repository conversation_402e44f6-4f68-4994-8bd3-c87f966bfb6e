from flask import Blueprint, request, jsonify, session, current_app
from flask_login import <PERSON><PERSON><PERSON><PERSON><PERSON>, login_user, logout_user, login_required, current_user
from flask_wtf import FlaskForm
from wtforms import StringField, PasswordField, BooleanField, ValidationError
from wtforms.validators import DataRequired, Email, Length, EqualTo
from werkzeug.security import generate_password_hash
import jwt
from datetime import datetime, timedelta
from functools import wraps
import re

from src.models.models import db, User, UserLocation, Vehicle
from src.security.audit_log import AuditLogger

# Initialize Flask-Login
login_manager = LoginManager()
login_manager.login_view = 'login_page'  # Points to the GET route for login page
login_manager.login_message = 'Por favor inicia sesión para acceder a esta página.'
login_manager.login_message_category = 'info'

@login_manager.user_loader
def load_user(user_id):
    return User.query.get(int(user_id))

# Create auth blueprint
auth_bp = Blueprint('auth', __name__, url_prefix='/auth')

# Forms
class LoginForm(FlaskForm):
    email = StringField('Email', validators=[
        DataRequired(message='El email es requerido'),
        Email(message='Ingresa un email válido')
    ])
    password = PasswordField('Contraseña', validators=[
        DataRequired(message='La contraseña es requerida')
    ])
    remember_me = BooleanField('Recordarme')

class RegisterForm(FlaskForm):
    email = StringField('Email', validators=[
        DataRequired(message='El email es requerido'),
        Email(message='Ingresa un email válido')
    ])
    first_name = StringField('Nombre', validators=[
        DataRequired(message='El nombre es requerido'),
        Length(min=2, max=100, message='El nombre debe tener entre 2 y 100 caracteres')
    ])
    last_name = StringField('Apellido', validators=[
        DataRequired(message='El apellido es requerido'),
        Length(min=2, max=100, message='El apellido debe tener entre 2 y 100 caracteres')
    ])
    phone = StringField('Teléfono', validators=[
        Length(max=20, message='El teléfono no puede tener más de 20 caracteres')
    ])
    password = PasswordField('Contraseña', validators=[
        DataRequired(message='La contraseña es requerida'),
        Length(min=8, message='La contraseña debe tener al menos 8 caracteres')
    ])
    password_confirm = PasswordField('Confirmar Contraseña', validators=[
        DataRequired(message='Confirma tu contraseña'),
        EqualTo('password', message='Las contraseñas no coinciden')
    ])
    
    def validate_email(self, field):
        if User.query.filter_by(email=field.data.lower()).first():
            raise ValidationError('Este email ya está registrado')
    
    def validate_password(self, field):
        password = field.data
        if not re.search(r'[A-Za-z]', password):
            raise ValidationError('La contraseña debe contener al menos una letra')
        if not re.search(r'\d', password):
            raise ValidationError('La contraseña debe contener al menos un número')

# JWT Token utilities
def generate_jwt_token(user_id, expires_in=None):
    """Generate JWT token for API access"""
    if expires_in is None:
        expires_in = current_app.config.get('JWT_ACCESS_TOKEN_EXPIRES', 3600)
    
    payload = {
        'user_id': user_id,
        'exp': datetime.utcnow() + timedelta(seconds=expires_in),
        'iat': datetime.utcnow()
    }
    
    return jwt.encode(
        payload,
        current_app.config['JWT_SECRET_KEY'],
        algorithm='HS256'
    )

def verify_jwt_token(token):
    """Verify JWT token and return user"""
    try:
        payload = jwt.decode(
            token,
            current_app.config['JWT_SECRET_KEY'],
            algorithms=['HS256']
        )
        user = User.query.get(payload['user_id'])
        if user and user.is_active:
            return user
    except jwt.ExpiredSignatureError:
        return None
    except jwt.InvalidTokenError:
        return None
    return None

# Decorators
def jwt_required(f):
    """Decorator to require JWT token for API endpoints"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        token = None
        auth_header = request.headers.get('Authorization')
        
        if auth_header:
            try:
                token = auth_header.split(' ')[1]  # Bearer <token>
            except IndexError:
                return jsonify({'error': 'Token inválido'}), 401
        
        if not token:
            return jsonify({'error': 'Token requerido'}), 401
        
        user = verify_jwt_token(token)
        if not user:
            return jsonify({'error': 'Token inválido o expirado'}), 401
        
        # Make user available in the request context
        request.current_user = user
        return f(*args, **kwargs)
    
    return decorated_function

def admin_required(f):
    """Decorator to require admin privileges"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        # Check for JWT user first (API endpoints)
        if hasattr(request, 'current_user'):
            user = request.current_user
            if not user or not user.is_admin:
                return jsonify({'error': 'Acceso de administrador requerido'}), 403
        # Fallback to Flask-Login current_user (web endpoints)
        elif not current_user.is_authenticated or not current_user.is_admin:
            if request.is_json:
                return jsonify({'error': 'Acceso de administrador requerido'}), 403
            return jsonify({'error': 'Acceso denegado'}), 403
        return f(*args, **kwargs)
    return decorated_function

def location_access_required(location_name=None):
    """Decorator to require access to a specific location"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            # For JWT-protected endpoints
            if hasattr(request, 'current_user'):
                user = request.current_user
            # For session-protected endpoints
            elif current_user.is_authenticated:
                user = current_user
            else:
                return jsonify({'error': 'Autenticación requerida'}), 401

            # Admin has access to all locations
            if user.is_admin:
                return f(*args, **kwargs)

            # If no specific location required, just check if user has any location access
            if location_name is None:
                if user.locations:
                    return f(*args, **kwargs)
                return jsonify({'error': 'Acceso a ubicación requerido'}), 403

            # Check if user has access to the specific location
            if user.has_access_to_location(location_name):
                return f(*args, **kwargs)

            return jsonify({'error': f'No tienes acceso a la ubicación: {location_name}'}), 403

        return decorated_function
    return decorator

def resource_owner_or_admin_required(resource_user_field='user_id'):
    """Decorator to require user to be owner of resource or admin"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            # Get current user
            if hasattr(request, 'current_user'):
                user = request.current_user
            elif current_user.is_authenticated:
                user = current_user
            else:
                return jsonify({'error': 'Autenticación requerida'}), 401

            # Admin can access any resource
            if user.is_admin:
                return f(*args, **kwargs)

            # For resource ownership check, we need to get the resource first
            # This will be handled in the endpoint itself
            request.resource_owner_check = {
                'user_id': user.id,
                'field': resource_user_field
            }

            return f(*args, **kwargs)

        return decorated_function
    return decorator

# Routes
@auth_bp.route('/login', methods=['POST'])
def login():
    """User login endpoint"""
    try:
        if request.is_json:
            data = request.get_json()
            email = data.get('email', '').strip().lower()
            password = data.get('password', '')
            remember = data.get('remember', False)
        else:
            form = LoginForm()
            if not form.validate_on_submit():
                return jsonify({'error': 'Datos inválidos', 'errors': form.errors}), 400
            email = form.email.data.strip().lower()
            password = form.password.data
            remember = form.remember_me.data
        
        if not email or not password:
            return jsonify({'error': 'Email y contraseña son requeridos'}), 400
        
        user = User.query.filter_by(email=email).first()
        
        if not user or not user.check_password(password):
            # Log failed login attempt
            AuditLogger.log_authentication(
                'login_failed',
                email,
                success=False,
                error_message='Invalid credentials'
            )
            return jsonify({'error': 'Email o contraseña incorrectos'}), 401

        if not user.is_active:
            # Log inactive account login attempt
            AuditLogger.log_authentication(
                'login_failed',
                email,
                success=False,
                error_message='Account deactivated'
            )
            return jsonify({'error': 'Cuenta desactivada'}), 401
        
        # Update last login
        user.last_login = datetime.utcnow()
        db.session.commit()
        
        # Login user for session-based auth
        login_user(user, remember=remember)

        # Ensure session is saved
        session.permanent = True
        session['user_id'] = user.id
        session['user_email'] = user.email

        # Generate JWT token for API access
        jwt_token = generate_jwt_token(user.id)

        # Log successful login
        AuditLogger.log_authentication(
            'login_success',
            user.email,
            success=True,
            additional_data={'remember_me': remember}
        )

        return jsonify({
            'success': True,
            'message': 'Inicio de sesión exitoso',
            'user': user.to_dict(),
            'token': jwt_token
        })
        
    except Exception as e:
        current_app.logger.error(f"Login error: {str(e)}")
        return jsonify({'error': 'Error interno del servidor'}), 500

@auth_bp.route('/register', methods=['POST'])
def register():
    """User registration endpoint"""
    try:
        if request.is_json:
            data = request.get_json()
            form_data = {
                'email': data.get('email', '').strip().lower(),
                'first_name': data.get('first_name', '').strip(),
                'last_name': data.get('last_name', '').strip(),
                'phone': data.get('phone', '').strip(),
                'password': data.get('password', ''),
                'password_confirm': data.get('password_confirm', '')
            }
        else:
            form = RegisterForm()
            if not form.validate_on_submit():
                return jsonify({'error': 'Datos inválidos', 'errors': form.errors}), 400
            form_data = {
                'email': form.email.data.strip().lower(),
                'first_name': form.first_name.data.strip(),
                'last_name': form.last_name.data.strip(),
                'phone': form.phone.data.strip() if form.phone.data else None,
                'password': form.password.data,
                'password_confirm': form.password_confirm.data
            }
        
        # Validate required fields
        required_fields = ['email', 'first_name', 'last_name', 'password']
        for field in required_fields:
            if not form_data.get(field):
                return jsonify({'error': f'{field} es requerido'}), 400
        
        # Check if passwords match
        if form_data['password'] != form_data['password_confirm']:
            return jsonify({'error': 'Las contraseñas no coinciden'}), 400
        
        # Check if user already exists
        if User.query.filter_by(email=form_data['email']).first():
            return jsonify({'error': 'Este email ya está registrado'}), 400
        
        # Create new user
        user = User(
            email=form_data['email'],
            first_name=form_data['first_name'],
            last_name=form_data['last_name'],
            phone=form_data['phone'],
            is_active=True,
            email_verified=False  # Require email verification in production
        )
        user.set_password(form_data['password'])
        
        db.session.add(user)
        db.session.commit()
        
        current_app.logger.info(f"New user registered: {user.email}")
        
        return jsonify({
            'success': True,
            'message': 'Usuario registrado exitosamente',
            'user': user.to_dict()
        }), 201
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Registration error: {str(e)}")
        return jsonify({'error': 'Error interno del servidor'}), 500

@auth_bp.route('/logout', methods=['POST'])
@login_required
def logout():
    """User logout endpoint"""
    logout_user()
    return jsonify({'success': True, 'message': 'Sesión cerrada exitosamente'})

@auth_bp.route('/me', methods=['GET'])
@login_required
def get_current_user():
    """Get current user information"""
    return jsonify({
        'success': True,
        'user': current_user.to_dict()
    })

@auth_bp.route('/me', methods=['PUT'])
@login_required
def update_current_user():
    """Update current user information"""
    try:
        data = request.get_json()
        
        # Update allowed fields
        if 'first_name' in data:
            current_user.first_name = data['first_name'].strip()
        if 'last_name' in data:
            current_user.last_name = data['last_name'].strip()
        if 'phone' in data:
            current_user.phone = data['phone'].strip() if data['phone'] else None
        
        current_user.updated_at = datetime.utcnow()
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': 'Perfil actualizado exitosamente',
            'user': current_user.to_dict()
        })
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Profile update error: {str(e)}")
        return jsonify({'error': 'Error interno del servidor'}), 500

@auth_bp.route('/change-password', methods=['POST'])
@login_required
def change_password():
    """Change user password"""
    try:
        data = request.get_json()
        current_password = data.get('current_password')
        new_password = data.get('new_password')
        confirm_password = data.get('confirm_password')
        
        if not all([current_password, new_password, confirm_password]):
            return jsonify({'error': 'Todos los campos son requeridos'}), 400
        
        if not current_user.check_password(current_password):
            return jsonify({'error': 'Contraseña actual incorrecta'}), 400
        
        if new_password != confirm_password:
            return jsonify({'error': 'Las contraseñas nuevas no coinciden'}), 400
        
        if len(new_password) < 8:
            return jsonify({'error': 'La nueva contraseña debe tener al menos 8 caracteres'}), 400
        
        current_user.set_password(new_password)
        current_user.updated_at = datetime.utcnow()
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': 'Contraseña actualizada exitosamente'
        })
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Password change error: {str(e)}")
        return jsonify({'error': 'Error interno del servidor'}), 500

# Utility functions
def init_auth(app):
    """Initialize authentication with Flask app"""
    login_manager.init_app(app)
    app.register_blueprint(auth_bp)
