"""
Input validation and sanitization utilities for VecinoSeguro
"""

import re
import html
import urllib.parse
from datetime import datetime, timedelta
from flask import jsonify, request
import bleach
from src.security.audit_log import AuditLogger

class ValidationError(Exception):
    """Custom validation error"""
    pass

class InputValidator:
    """Input validation and sanitization utilities"""
    
    # Regex patterns
    LICENSE_PLATE_PATTERN = re.compile(r'^(?=.*[A-Z])[A-Z0-9]{3,10}$')
    EMAIL_PATTERN = re.compile(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$')
    PHONE_PATTERN = re.compile(r'^\+?[\d\s\-\(\)]{7,20}$')
    NAME_PATTERN = re.compile(r'^[a-zA-ZáéíóúÁÉÍÓÚñÑ\s]{2,100}$')
    
    @staticmethod
    def sanitize_string(value, max_length=255, allow_html=False, strict_mode=False):
        """Sanitize string input with enhanced security"""
        if not value:
            return ''

        # Convert to string and strip whitespace
        value = str(value).strip()

        # Limit length
        if len(value) > max_length:
            raise ValidationError(f'Texto demasiado largo (máximo {max_length} caracteres)')

        # Check for malicious patterns in strict mode
        if strict_mode:
            InputValidator._check_malicious_patterns(value)

        # Remove HTML if not allowed
        if not allow_html:
            value = bleach.clean(value, tags=[], strip=True)
            # Additional HTML entity decoding and re-encoding
            value = html.unescape(value)
            value = html.escape(value)

        # URL decode to catch encoded attacks
        try:
            decoded = urllib.parse.unquote(value)
            if decoded != value:
                # Check decoded version for attacks
                InputValidator._check_malicious_patterns(decoded)
        except:
            pass

        return value

    @staticmethod
    def _check_malicious_patterns(value):
        """Check for malicious patterns in input"""
        if not value:
            return

        value_lower = value.lower()

        # SQL injection patterns
        sql_patterns = [
            r"union\s+select", r"drop\s+table", r"insert\s+into", r"delete\s+from",
            r"update\s+set", r"exec\s*\(", r"execute\s*\(", r"sp_executesql",
            r"xp_cmdshell", r";\s*drop", r";\s*delete", r";\s*insert",
            r"'\s*or\s*'", r'"\s*or\s*"', r"'\s*and\s*'", r'"\s*and\s*"',
            r"'\s*=\s*'", r'"\s*=\s*"', r"--", r"/\*", r"\*/",
            r"char\s*\(", r"ascii\s*\(", r"substring\s*\(", r"waitfor\s+delay"
        ]

        # XSS patterns
        xss_patterns = [
            r"<script", r"</script>", r"javascript:", r"vbscript:", r"onload\s*=",
            r"onerror\s*=", r"onclick\s*=", r"onmouseover\s*=", r"onfocus\s*=",
            r"alert\s*\(", r"confirm\s*\(", r"prompt\s*\(", r"document\.cookie",
            r"document\.write", r"eval\s*\(", r"expression\s*\(", r"<iframe",
            r"<object", r"<embed", r"<applet", r"<meta", r"<link"
        ]

        # Command injection patterns
        cmd_patterns = [
            r";\s*rm\s+", r";\s*cat\s+", r";\s*ls\s+", r";\s*pwd", r";\s*whoami",
            r"\|\s*nc\s+", r"\|\s*netcat\s+", r"&&\s*rm\s+", r"\|\s*wget\s+",
            r"\|\s*curl\s+", r">\s*/dev/", r"<\s*/dev/", r"\$\(", r"`.*`"
        ]

        all_patterns = sql_patterns + xss_patterns + cmd_patterns

        for pattern in all_patterns:
            if re.search(pattern, value_lower, re.IGNORECASE):
                # Log the attack attempt
                AuditLogger.log_system_event(
                    'malicious_input_detected',
                    f"Malicious pattern detected in input: {pattern}",
                    success=False,
                    additional_data={
                        'pattern': pattern,
                        'input_sample': value[:100],  # First 100 chars
                        'ip_address': request.remote_addr if request else 'unknown',
                        'endpoint': request.endpoint if request else 'unknown'
                    }
                )
                raise ValidationError('Entrada contiene patrones maliciosos')
    
    @staticmethod
    def validate_license_plate(plate):
        """Validate license plate format"""
        if not plate:
            raise ValidationError('Placa requerida')
        
        plate = plate.upper().strip()
        
        if not InputValidator.LICENSE_PLATE_PATTERN.match(plate):
            raise ValidationError('Formato de placa inválido (3-10 caracteres alfanuméricos)')
        
        return plate
    
    @staticmethod
    def validate_email(email):
        """Validate email format"""
        if not email:
            raise ValidationError('Email requerido')
        
        email = email.lower().strip()
        
        if not InputValidator.EMAIL_PATTERN.match(email):
            raise ValidationError('Formato de email inválido')
        
        if len(email) > 120:
            raise ValidationError('Email demasiado largo')
        
        return email
    
    @staticmethod
    def validate_phone(phone):
        """Validate phone number format"""
        if not phone:
            return None
        
        phone = phone.strip()
        
        if not InputValidator.PHONE_PATTERN.match(phone):
            raise ValidationError('Formato de teléfono inválido')
        
        return phone
    
    @staticmethod
    def validate_name(name, field_name='Nombre'):
        """Validate name fields"""
        if not name:
            raise ValidationError(f'{field_name} requerido')
        
        name = name.strip()
        
        if not InputValidator.NAME_PATTERN.match(name):
            raise ValidationError(f'{field_name} contiene caracteres inválidos')
        
        if len(name) < 2:
            raise ValidationError(f'{field_name} debe tener al menos 2 caracteres')
        
        return name
    
    @staticmethod
    def validate_password(password):
        """Validate password strength"""
        if not password:
            raise ValidationError('Contraseña requerida')
        
        if len(password) < 8:
            raise ValidationError('La contraseña debe tener al menos 8 caracteres')
        
        if len(password) > 128:
            raise ValidationError('La contraseña es demasiado larga')
        
        # Check for at least one letter and one number
        if not re.search(r'[A-Za-z]', password):
            raise ValidationError('La contraseña debe contener al menos una letra')
        
        if not re.search(r'\d', password):
            raise ValidationError('La contraseña debe contener al menos un número')
        
        return password
    
    @staticmethod
    def validate_vehicle_data(data):
        """Validate vehicle data"""
        validated = {}
        
        # Required fields
        validated['license_plate'] = InputValidator.validate_license_plate(
            data.get('plate') or data.get('license_plate')
        )
        
        # Optional fields
        validated['make'] = InputValidator.sanitize_string(data.get('make', ''), 50) or 'Unknown'
        validated['model'] = InputValidator.sanitize_string(data.get('model', ''), 50) or 'Unknown'
        validated['color'] = InputValidator.sanitize_string(data.get('color', ''), 30) or 'Unknown'
        
        # Year validation
        year = data.get('year')
        if year:
            try:
                year = int(year)
                current_year = datetime.now().year
                if year < 1900 or year > current_year + 2:
                    raise ValidationError(f'Año inválido (debe estar entre 1900 y {current_year + 2})')
                validated['year'] = year
            except (ValueError, TypeError):
                raise ValidationError('Año debe ser un número válido')
        else:
            validated['year'] = None
        
        # Vehicle type validation
        valid_types = ['car', 'motorcycle', 'truck', 'van', 'suv', 'bus']
        vehicle_type = data.get('vehicle_type', 'car').lower()
        if vehicle_type not in valid_types:
            vehicle_type = 'car'
        validated['vehicle_type'] = vehicle_type
        
        # Temporary status
        validated['is_temporary'] = bool(data.get('is_temporary', False))
        
        return validated
    
    @staticmethod
    def validate_user_data(data, is_update=False):
        """Validate user registration/update data"""
        validated = {}
        
        # Email (required for registration, optional for updates)
        if 'email' in data:
            validated['email'] = InputValidator.validate_email(data['email'])
        elif not is_update:
            raise ValidationError('Email requerido')
        
        # Names
        if 'first_name' in data:
            validated['first_name'] = InputValidator.validate_name(data['first_name'], 'Nombre')
        elif not is_update:
            raise ValidationError('Nombre requerido')
        
        if 'last_name' in data:
            validated['last_name'] = InputValidator.validate_name(data['last_name'], 'Apellido')
        elif not is_update:
            raise ValidationError('Apellido requerido')
        
        # Phone (optional)
        if 'phone' in data:
            validated['phone'] = InputValidator.validate_phone(data['phone'])
        
        # Password (required for registration)
        if 'password' in data:
            validated['password'] = InputValidator.validate_password(data['password'])
        elif not is_update:
            raise ValidationError('Contraseña requerida')
        
        return validated
    
    @staticmethod
    def validate_location_data(data):
        """Validate location data"""
        validated = {}
        
        # Required fields
        location_name = data.get('location_name', '').strip()
        if not location_name:
            raise ValidationError('Nombre de ubicación requerido')
        
        validated['location_name'] = InputValidator.sanitize_string(location_name, 100)
        
        # Optional fields
        validated['location_address'] = InputValidator.sanitize_string(
            data.get('location_address', ''), 255
        )
        
        # Access level validation
        valid_levels = ['resident', 'guest', 'admin']
        access_level = data.get('access_level', 'resident').lower()
        if access_level not in valid_levels:
            access_level = 'resident'
        validated['access_level'] = access_level
        
        # User ID validation
        user_id = data.get('user_id')
        if user_id:
            try:
                validated['user_id'] = int(user_id)
            except (ValueError, TypeError):
                raise ValidationError('ID de usuario inválido')
        
        # Expiration for guest access
        if access_level == 'guest':
            expires_days = data.get('expires_days', 30)
            try:
                expires_days = int(expires_days)
                if expires_days < 1 or expires_days > 365:
                    expires_days = 30
                validated['expires_at'] = datetime.utcnow() + timedelta(days=expires_days)
            except (ValueError, TypeError):
                validated['expires_at'] = datetime.utcnow() + timedelta(days=30)
        
        return validated
    
    @staticmethod
    def validate_pagination_params(request_args):
        """Validate pagination parameters"""
        try:
            page = int(request_args.get('page', 1))
            if page < 1:
                page = 1
        except (ValueError, TypeError):
            page = 1
        
        try:
            per_page = int(request_args.get('per_page', 20))
            if per_page < 1:
                per_page = 20
            elif per_page > 100:  # Limit to prevent abuse
                per_page = 100
        except (ValueError, TypeError):
            per_page = 20
        
        return page, per_page
    
    @staticmethod
    def validate_time_range_params(request_args):
        """Validate time range parameters"""
        try:
            hours = int(request_args.get('hours', 24))
            if hours < 1:
                hours = 1
            elif hours > 168:  # Max 1 week
                hours = 168
        except (ValueError, TypeError):
            hours = 24
        
        try:
            limit = int(request_args.get('limit', 100))
            if limit < 1:
                limit = 1
            elif limit > 500:  # Prevent abuse
                limit = 500
        except (ValueError, TypeError):
            limit = 100
        
        return hours, limit

def handle_validation_error(func):
    """Decorator to handle validation errors"""
    from functools import wraps
    from flask import current_app

    @wraps(func)
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except ValidationError as e:
            return jsonify({'error': str(e)}), 400
        except Exception as e:
            # Log unexpected errors
            current_app.logger.error(f"Unexpected error in {func.__name__}: {str(e)}")
            return jsonify({'error': 'Error interno del servidor'}), 500
    return wrapper
