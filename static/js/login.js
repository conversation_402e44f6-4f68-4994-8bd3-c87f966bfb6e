/**
 * VecinoSeguro Login Page JavaScript
 * Handles user authentication and form interactions
 */

document.addEventListener("DOMContentLoaded", function () {
  const loginForm = document.getElementById("loginForm");
  const loginBtn = document.getElementById("loginBtn");
  const loginSpinner = document.getElementById("loginSpinner");
  const loginText = document.getElementById("loginText");
  const alertContainer = document.getElementById("alert-container");

  /**
   * Display alert message to user
   * @param {string} message - Message to display
   * @param {string} type - Alert type ('error' or 'success')
   */
  function showAlert(message, type = "error") {
    const alertClass = type === "error" ? "alert-error" : "alert-success";
    alertContainer.innerHTML = `
      <div class="alert ${alertClass}">
        <i class="fas fa-${
          type === "error" ? "exclamation-circle" : "check-circle"
        }"></i>
        ${message}
      </div>
    `;
  }

  /**
   * Set loading state for login button
   * @param {boolean} loading - Whether to show loading state
   */
  function setLoading(loading) {
    loginBtn.disabled = loading;
    if (loading) {
      loginSpinner.classList.add("show");
      loginText.textContent = "Iniciando sesión...";
    } else {
      loginSpinner.classList.remove("show");
      loginText.textContent = "Iniciar Sesión";
    }
  }

  /**
   * Handle login form submission
   */
  loginForm.addEventListener("submit", async function (e) {
    e.preventDefault();

    const formData = new FormData(loginForm);
    const data = {
      email: formData.get("email"),
      password: formData.get("password"),
      remember: formData.get("remember") === "on",
    };

    setLoading(true);
    alertContainer.innerHTML = "";

    try {
      const response = await fetch("/auth/login", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        credentials: "include", // Include cookies for session
        body: JSON.stringify(data),
      });

      const result = await response.json();

      if (response.ok && result.success) {
        showAlert("Inicio de sesión exitoso. Redirigiendo...", "success");

        // Store JWT token if provided
        if (result.token) {
          localStorage.setItem("authToken", result.token);
        }

        // Redirect to dashboard
        setTimeout(() => {
          window.location.href = "/";
        }, 1000);
      } else {
        showAlert(result.error || "Error al iniciar sesión");
      }
    } catch (error) {
      console.error("Login error:", error);
      showAlert("Error de conexión. Por favor intenta de nuevo.");
    } finally {
      setLoading(false);
    }
  });

  /**
   * Check if user is already logged in
   */
  function checkExistingAuth() {
    // Disabled to prevent infinite redirect loops
    // The dashboard will handle authentication validation
    console.log("Authentication check disabled on login page");
  }

  // Check for existing authentication on page load
  checkExistingAuth();
});
