// <PERSON>gar datos al iniciar
loadStats();
loadLogs();
loadPlates();

// Actualizar cada 60 segundos (reducido para evitar rate limiting)
setInterval(() => {
  loadStats();
  loadLogs();
}, 60000);

function showTab(tabName) {
  // Ocultar todas las pestañas
  document
    .querySelectorAll(".tab-content")
    .forEach((tab) => (tab.style.display = "none"));
  document
    .querySelectorAll(".tab")
    .forEach((tab) => tab.classList.remove("active"));

  // Mostrar pestaña seleccionada
  document.getElementById(tabName + "-tab").style.display = "block";
  event.target.classList.add("active");

  if (tabName === "plates") loadPlates();
}

async function loadStats() {
  try {
    const response = await fetch("/api/stats");
    const stats = await response.json();

    // Calculate percentages and trends
    const totalAccess = stats.total_accesos || 0;
    const authorizedPercent =
      totalAccess > 0 ? Math.round((stats.autorizados / totalAccess) * 100) : 0;
    const deniedPercent =
      totalAccess > 0
        ? Math.round((stats.no_autorizados / totalAccess) * 100)
        : 0;

    document.getElementById("stats").innerHTML = `
                    <div class="stat-card">
                        <div class="stat-icon" style="background: var(--primary-color);">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <div class="stat-number">${
                          stats.total_accesos || 0
                        }</div>
                        <div class="stat-label">Total de Accesos</div>
                        <div class="stat-change positive">
                            <i class="fas fa-arrow-up"></i> Últimas 24h
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon" style="background: var(--success-color);">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="stat-number">${stats.autorizados || 0}</div>
                        <div class="stat-label">Accesos Autorizados</div>
                        <div class="stat-change positive">
                            <i class="fas fa-percentage"></i> ${authorizedPercent}% del total
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon" style="background: var(--danger-color);">
                            <i class="fas fa-times-circle"></i>
                        </div>
                        <div class="stat-number">${
                          stats.no_autorizados || 0
                        }</div>
                        <div class="stat-label">Accesos Denegados</div>
                        <div class="stat-change negative">
                            <i class="fas fa-percentage"></i> ${deniedPercent}% del total
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon" style="background: var(--secondary-color);">
                            <i class="fas fa-sign-in-alt"></i>
                        </div>
                        <div class="stat-number">${stats.entradas || 0}</div>
                        <div class="stat-label">Entradas Registradas</div>
                        <div class="stat-change">
                            <i class="fas fa-clock"></i> Hoy
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon" style="background: var(--warning-color);">
                            <i class="fas fa-sign-out-alt"></i>
                        </div>
                        <div class="stat-number">${stats.salidas || 0}</div>
                        <div class="stat-label">Salidas Registradas</div>
                        <div class="stat-change">
                            <i class="fas fa-clock"></i> Hoy
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon" style="background: var(--gray-600);">
                            <i class="fas fa-car"></i>
                        </div>
                        <div class="stat-number">${
                          stats.total_placas || 0
                        }</div>
                        <div class="stat-label">Placas Registradas</div>
                        <div class="stat-change">
                            <i class="fas fa-database"></i> En sistema
                        </div>
                    </div>
                `;
  } catch (error) {
    console.error("Error cargando estadísticas:", error);
    document.getElementById("stats").innerHTML = `
                    <div class="empty-state">
                        <i class="fas fa-exclamation-triangle"></i>
                        <p>Error al cargar las estadísticas</p>
                    </div>
                `;
  }
}

async function loadLogs() {
  try {
    const hours = document.getElementById("hours-filter").value;
    const response = await fetch(`/api/logs?hours=${hours}`);
    const logs = await response.json();

    if (logs.length === 0) {
      document.getElementById("logs-container").innerHTML = `
                        <div class="empty-state">
                            <i class="fas fa-inbox"></i>
                            <h4>No hay registros</h4>
                            <p>No se encontraron registros de acceso en el período seleccionado.</p>
                        </div>
                    `;
      return;
    }

    let html = `
                    <table class="table">
                        <thead>
                            <tr>
                                <th><i class="fas fa-clock"></i> Fecha y Hora</th>
                                <th><i class="fas fa-map-marker-alt"></i> Ubicación</th>
                                <th><i class="fas fa-car"></i> Placa</th>
                                <th><i class="fas fa-user"></i> Propietario</th>
                                <th><i class="fas fa-shield-alt"></i> Estado</th>
                                <th><i class="fas fa-camera"></i> Imagen</th>
                            </tr>
                        </thead>
                        <tbody>
                `;

    logs.forEach((log) => {
      const date = new Date(log.timestamp);
      const formattedDate = date.toLocaleDateString("es-ES", {
        day: "2-digit",
        month: "2-digit",
        year: "numeric",
      });
      const formattedTime = date.toLocaleTimeString("es-ES", {
        hour: "2-digit",
        minute: "2-digit",
        second: "2-digit",
      });

      const location = log.ubicacion || "N/A";
      const plate = log.placa || "Sin placa detectada";
      const owner = log.propietario || "Desconocido";
      const status = log.autorizado ? "Autorizado" : "Denegado";
      const statusClass = log.autorizado
        ? "status-authorized"
        : "status-denied";
      const locationClass =
        location === "entrada" ? "location-entrada" : "location-salida";

      // Construir enlace de imagen usando el nuevo endpoint
      const imageLink = log.imagen
        ? `<a href="/images/${log.imagen}" target="_blank" class="image-link">
                            <i class="fas fa-eye"></i> Ver imagen
                         </a>`
        : `<span style="color: var(--gray-400);">
                            <i class="fas fa-times"></i> Sin imagen
                         </span>`;

      html += `
                        <tr>
                            <td>
                                <div style="font-weight: 500;">${formattedDate}</div>
                                <div style="font-size: 0.75rem; color: var(--gray-500);">${formattedTime}</div>
                            </td>
                            <td>
                                <span class="location-badge ${locationClass}">
                                    <i class="fas fa-${
                                      location === "entrada"
                                        ? "sign-in-alt"
                                        : "sign-out-alt"
                                    }"></i>
                                    ${location.toUpperCase()}
                                </span>
                            </td>
                            <td>
                                <div style="font-weight: 500; font-family: monospace;">${plate}</div>
                            </td>
                            <td>
                                <div style="font-weight: 500;">${owner}</div>
                            </td>
                            <td>
                                <span class="status-badge ${statusClass}">
                                    <i class="fas fa-${
                                      log.autorizado ? "check" : "times"
                                    }"></i>
                                    ${status}
                                </span>
                            </td>
                            <td>${imageLink}</td>
                        </tr>
                    `;
    });

    html += "</tbody></table>";
    document.getElementById("logs-container").innerHTML = html;
  } catch (error) {
    console.error("Error cargando logs:", error);
    document.getElementById("logs-container").innerHTML = `
                    <div class="empty-state">
                        <i class="fas fa-exclamation-triangle"></i>
                        <h4>Error al cargar registros</h4>
                        <p>No se pudieron cargar los registros de acceso. Intente nuevamente.</p>
                    </div>
                `;
  }
}

async function loadPlates() {
  try {
    const response = await fetch("/api/plates");
    const plates = await response.json();

    const plateEntries = Object.entries(plates);

    if (plateEntries.length === 0) {
      document.getElementById("plates-container").innerHTML = `
                        <div class="empty-state">
                            <i class="fas fa-car"></i>
                            <h4>No hay placas registradas</h4>
                            <p>Agregue placas autorizadas usando el formulario de arriba.</p>
                        </div>
                    `;
      return;
    }

    let html = `
                    <table class="table">
                        <thead>
                            <tr>
                                <th><i class="fas fa-hashtag"></i> Placa</th>
                                <th><i class="fas fa-user"></i> Propietario</th>
                                <th><i class="fas fa-phone"></i> Teléfono</th>
                                <th><i class="fas fa-calendar"></i> Fecha de Registro</th>
                                <th><i class="fas fa-cogs"></i> Acciones</th>
                            </tr>
                        </thead>
                        <tbody>
                `;

    plateEntries.forEach(([plate, info]) => {
      const registrationDate = info.created_at
        ? new Date(info.created_at).toLocaleDateString("es-ES")
        : "No disponible";

      html += `
                        <tr>
                            <td>
                                <div style="font-weight: 600; font-family: monospace; font-size: 1rem; color: var(--primary-color);">
                                    ${plate}
                                </div>
                            </td>
                            <td>
                                <div style="font-weight: 500;">
                                    ${info.name || "Sin nombre"}
                                </div>
                            </td>
                            <td>
                                <div style="color: var(--gray-600);">
                                    ${info.phone || "No registrado"}
                                </div>
                            </td>
                            <td>
                                <div style="color: var(--gray-600); font-size: 0.875rem;">
                                    ${registrationDate}
                                </div>
                            </td>
                            <td>
                                <button class="btn btn-danger" onclick="deletePlate('${plate}')" title="Eliminar placa">
                                    <i class="fas fa-trash"></i> Eliminar
                                </button>
                            </td>
                        </tr>
                    `;
    });

    html += "</tbody></table>";
    document.getElementById("plates-container").innerHTML = html;
  } catch (error) {
    console.error("Error cargando placas:", error);
    document.getElementById("plates-container").innerHTML = `
                    <div class="empty-state">
                        <i class="fas fa-exclamation-triangle"></i>
                        <h4>Error al cargar placas</h4>
                        <p>No se pudieron cargar las placas registradas. Intente nuevamente.</p>
                    </div>
                `;
  }
}

async function addPlate() {
  const plate = document.getElementById("new-plate").value.trim().toUpperCase();
  const name = document.getElementById("new-name").value.trim();
  const phone = document.getElementById("new-phone").value.trim();

  if (!plate) {
    alert("Por favor ingresa una placa");
    return;
  }

  if (!name) {
    alert("Por favor ingresa el nombre del propietario");
    return;
  }

  // Validate plate format (basic validation)
  const plateRegex = /^[A-Z0-9]{6,8}$/;
  if (!plateRegex.test(plate)) {
    alert(
      "Formato de placa inválido. Use solo letras y números (6-8 caracteres)"
    );
    return;
  }

  try {
    const response = await fetch("/api/plates", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({ plate, name, phone }),
    });

    if (response.ok) {
      document.getElementById("new-plate").value = "";
      document.getElementById("new-name").value = "";
      document.getElementById("new-phone").value = "";
      loadPlates();
      loadStats();

      // Show success message with better styling
      const successMsg = document.createElement("div");
      successMsg.style.cssText = `
              position: fixed;
              top: 20px;
              right: 20px;
              background: var(--success-color);
              color: white;
              padding: 1rem 1.5rem;
              border-radius: var(--border-radius);
              box-shadow: var(--shadow-lg);
              z-index: 1000;
              font-weight: 500;
            `;
      successMsg.innerHTML =
        '<i class="fas fa-check"></i> Placa agregada exitosamente';
      document.body.appendChild(successMsg);

      setTimeout(() => {
        successMsg.remove();
      }, 3000);
    } else {
      const errorData = await response.json();
      alert(`Error: ${errorData.error || "Error agregando placa"}`);
    }
  } catch (error) {
    console.error("Error:", error);
    alert("Error de conexión. Verifique su conexión a internet.");
  }
}

async function deletePlate(plate) {
  if (
    !confirm(
      `¿Estás seguro de eliminar la placa ${plate}?\n\nEsta acción no se puede deshacer.`
    )
  )
    return;

  try {
    const response = await fetch(`/api/plates/${plate}`, {
      method: "DELETE",
    });

    if (response.ok) {
      loadPlates();
      loadStats();

      // Show success message
      const successMsg = document.createElement("div");
      successMsg.style.cssText = `
              position: fixed;
              top: 20px;
              right: 20px;
              background: var(--danger-color);
              color: white;
              padding: 1rem 1.5rem;
              border-radius: var(--border-radius);
              box-shadow: var(--shadow-lg);
              z-index: 1000;
              font-weight: 500;
            `;
      successMsg.innerHTML =
        '<i class="fas fa-trash"></i> Placa eliminada exitosamente';
      document.body.appendChild(successMsg);

      setTimeout(() => {
        successMsg.remove();
      }, 3000);
    } else {
      const errorData = await response.json();
      alert(`Error: ${errorData.error || "Error eliminando placa"}`);
    }
  } catch (error) {
    console.error("Error:", error);
    alert("Error de conexión. Verifique su conexión a internet.");
  }
}

async function openGateManually() {
  const duration = parseInt(document.getElementById("duration").value);

  if (
    !confirm(
      `⚠️ CONFIRMACIÓN DE SEGURIDAD\n\n¿Está completamente seguro de abrir el portón por ${duration} segundos?\n\nAsegúrese de que:\n• El área esté completamente despejada\n• No haya vehículos o personas en la zona\n• Sea seguro proceder con la apertura`
    )
  ) {
    return;
  }

  // Disable button during operation
  const button = event.target;
  const originalText = button.innerHTML;
  button.disabled = true;
  button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Abriendo...';

  try {
    const response = await fetch("/api/open-gate", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({ duration }),
    });

    const result = await response.json();

    if (response.ok) {
      // Show success message
      const successMsg = document.createElement("div");
      successMsg.style.cssText = `
              position: fixed;
              top: 20px;
              right: 20px;
              background: var(--success-color);
              color: white;
              padding: 1rem 1.5rem;
              border-radius: var(--border-radius);
              box-shadow: var(--shadow-lg);
              z-index: 1000;
              font-weight: 500;
            `;
      successMsg.innerHTML = `<i class="fas fa-check"></i> ${result.message}`;
      document.body.appendChild(successMsg);

      setTimeout(() => {
        successMsg.remove();
      }, 5000);

      loadLogs(); // Actualizar logs
    } else {
      alert(`❌ Error: ${result.error}`);
    }
  } catch (error) {
    console.error("Error:", error);
    alert("❌ Error de conexión. Verifique su conexión a internet.");
  } finally {
    // Re-enable button
    button.disabled = false;
    button.innerHTML = originalText;
  }
}

// Logout function
function logout() {
  if (confirm("¿Estás seguro de que quieres cerrar sesión?")) {
    localStorage.removeItem("authToken");
    window.location.href = "/login";
  }
}

// Mobile menu toggle (placeholder for future mobile menu implementation)
function toggleMobileMenu() {
  // For now, just show the user menu options in an alert
  // In a full implementation, this would show/hide a mobile menu
  const options = "Dashboard\nMi Perfil\nCerrar Sesión";
  alert("Menú:\n" + options);
}
