/**
 * VecinoSeguro Register Page JavaScript
 * Handles user registration, password validation, and form interactions
 */

document.addEventListener('DOMContentLoaded', function() {
    const registerForm = document.getElementById('registerForm');
    const registerBtn = document.getElementById('registerBtn');
    const registerSpinner = document.getElementById('registerSpinner');
    const registerText = document.getElementById('registerText');
    const alertContainer = document.getElementById('alert-container');
    const passwordInput = document.getElementById('password');
    const passwordConfirmInput = document.getElementById('password_confirm');
    const strengthFill = document.getElementById('strengthFill');
    const strengthText = document.getElementById('strengthText');

    /**
     * Display alert message to user
     * @param {string} message - Message to display
     * @param {string} type - Alert type ('error' or 'success')
     */
    function showAlert(message, type = 'error') {
        const alertClass = type === 'error' ? 'alert-error' : 'alert-success';
        alertContainer.innerHTML = `
            <div class="alert ${alertClass}">
                <i class="fas fa-${type === 'error' ? 'exclamation-circle' : 'check-circle'}"></i>
                ${message}
            </div>
        `;
    }

    /**
     * Set loading state for register button
     * @param {boolean} loading - Whether to show loading state
     */
    function setLoading(loading) {
        registerBtn.disabled = loading;
        if (loading) {
            registerSpinner.classList.add('show');
            registerText.textContent = 'Creando cuenta...';
        } else {
            registerSpinner.classList.remove('show');
            registerText.textContent = 'Crear Cuenta';
        }
    }

    /**
     * Check password strength and update UI
     * @param {string} password - Password to check
     * @returns {boolean} - Whether password is strong enough
     */
    function checkPasswordStrength(password) {
        let strength = 0;
        let text = 'Muy débil';
        let className = 'weak';

        // Check password criteria
        if (password.length >= 8) strength++;
        if (/[a-z]/.test(password)) strength++;
        if (/[A-Z]/.test(password)) strength++;
        if (/[0-9]/.test(password)) strength++;
        if (/[^A-Za-z0-9]/.test(password)) strength++;

        // Determine strength level
        switch (strength) {
            case 0:
            case 1:
                text = 'Muy débil';
                className = 'weak';
                break;
            case 2:
                text = 'Débil';
                className = 'weak';
                break;
            case 3:
                text = 'Buena';
                className = 'medium';
                break;
            case 4:
            case 5:
                text = 'Fuerte';
                className = 'strong';
                break;
        }

        // Update UI
        const strengthContainer = strengthFill.closest('.password-strength');
        strengthContainer.className = `password-strength ${className}`;
        strengthText.textContent = text;
        
        return strength >= 2; // Require at least "weak" strength
    }

    /**
     * Validate password confirmation matches
     */
    function validatePasswordConfirmation() {
        const password = passwordInput.value;
        const confirmPassword = passwordConfirmInput.value;
        
        if (confirmPassword && password !== confirmPassword) {
            passwordConfirmInput.classList.add('error');
            return false;
        } else {
            passwordConfirmInput.classList.remove('error');
            return true;
        }
    }

    /**
     * Validate email format
     * @param {string} email - Email to validate
     * @returns {boolean} - Whether email is valid
     */
    function validateEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    /**
     * Validate form data before submission
     * @param {Object} data - Form data to validate
     * @returns {boolean} - Whether form is valid
     */
    function validateForm(data) {
        // Check required fields
        if (!data.email || !data.first_name || !data.last_name || !data.password || !data.password_confirm) {
            showAlert('Todos los campos obligatorios deben ser completados');
            return false;
        }

        // Validate email format
        if (!validateEmail(data.email)) {
            showAlert('Por favor ingresa un email válido');
            return false;
        }

        // Validate passwords match
        if (data.password !== data.password_confirm) {
            showAlert('Las contraseñas no coinciden');
            return false;
        }

        // Check password strength
        if (!checkPasswordStrength(data.password)) {
            showAlert('La contraseña debe ser más fuerte (mínimo 8 caracteres con letras y números)');
            return false;
        }

        return true;
    }

    // Event Listeners
    
    /**
     * Password input event listener for strength checking
     */
    passwordInput.addEventListener('input', function() {
        const password = this.value;
        if (password) {
            checkPasswordStrength(password);
        } else {
            const strengthContainer = strengthFill.closest('.password-strength');
            strengthContainer.className = 'password-strength';
            strengthText.textContent = 'Ingresa una contraseña';
        }
        
        // Re-validate confirmation if it has a value
        if (passwordConfirmInput.value) {
            validatePasswordConfirmation();
        }
    });

    /**
     * Password confirmation input event listener
     */
    passwordConfirmInput.addEventListener('input', validatePasswordConfirmation);

    /**
     * Form submission handler
     */
    registerForm.addEventListener('submit', async function(e) {
        e.preventDefault();
        
        const formData = new FormData(registerForm);
        const data = {
            email: formData.get('email'),
            first_name: formData.get('first_name'),
            last_name: formData.get('last_name'),
            phone: formData.get('phone') || '',
            password: formData.get('password'),
            password_confirm: formData.get('password_confirm')
        };

        // Validate form
        if (!validateForm(data)) {
            return;
        }

        setLoading(true);
        alertContainer.innerHTML = '';

        try {
            const response = await fetch('/auth/register', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                credentials: 'include',
                body: JSON.stringify(data)
            });

            const result = await response.json();

            if (response.ok && result.success) {
                showAlert('Cuenta creada exitosamente. Redirigiendo al login...', 'success');
                
                // Clear form
                registerForm.reset();
                
                // Redirect to login
                setTimeout(() => {
                    window.location.href = '/login';
                }, 2000);
            } else {
                showAlert(result.error || 'Error al crear la cuenta');
            }
        } catch (error) {
            console.error('Registration error:', error);
            showAlert('Error de conexión. Por favor intenta de nuevo.');
        } finally {
            setLoading(false);
        }
    });
});
