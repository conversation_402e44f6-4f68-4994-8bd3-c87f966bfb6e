// Profile Management JavaScript
let currentUser = null;
let userVehicles = [];
let userLocations = [];

// Initialize page
document.addEventListener("DOMContentLoaded", function () {
  checkAuthentication();
  loadUserProfile();
  loadUserVehicles();
  loadUserLocations();

  // Setup form handlers
  setupFormHandlers();
});

// Authentication check
function checkAuthentication() {
  const token = localStorage.getItem("authToken");
  if (!token) {
    window.location.href = "/login";
    return;
  }

  // Set default authorization header
  window.authHeaders = {
    Authorization: `Bearer ${token}`,
    "Content-Type": "application/json",
  };
}

// Setup form event handlers
function setupFormHandlers() {
  // Profile form
  document
    .getElementById("profileForm")
    .addEventListener("submit", handleProfileUpdate);

  // Vehicle form
  document
    .getElementById("vehicleForm")
    .addEventListener("submit", handleVehicleSubmit);

  // Password form
  document
    .getElementById("passwordForm")
    .addEventListener("submit", handlePasswordChange);

  // License plate auto-uppercase
  document
    .getElementById("license_plate")
    .addEventListener("input", function (e) {
      e.target.value = e.target.value.toUpperCase();
    });
}

// Load user profile
async function loadUserProfile() {
  try {
    const response = await fetch("/auth/me", {
      headers: window.authHeaders,
    });

    if (!response.ok) {
      throw new Error("Failed to load profile");
    }

    const data = await response.json();
    currentUser = data.user;

    // Update UI
    updateProfileUI(currentUser);
  } catch (error) {
    console.error("Error loading profile:", error);
    showAlert("Error cargando perfil", "error");
  }
}

// Update profile UI
function updateProfileUI(user) {
  // Update avatar initials
  const initials = (
    user.first_name.charAt(0) + user.last_name.charAt(0)
  ).toUpperCase();
  document.getElementById("userAvatar").textContent = initials;

  // Update profile info
  document.getElementById("userName").textContent = user.full_name;
  document.getElementById("userEmail").textContent = user.email;

  // Update form fields
  document.getElementById("first_name").value = user.first_name;
  document.getElementById("last_name").value = user.last_name;
  document.getElementById("email").value = user.email;
  document.getElementById("phone").value = user.phone || "";

  // Update status badge
  const statusBadge = document.getElementById("userStatus");
  if (user.is_active) {
    statusBadge.textContent = "Activo";
    statusBadge.className = "badge badge-success";
  } else {
    statusBadge.textContent = "Inactivo";
    statusBadge.className = "badge badge-danger";
  }

  // Update admin badge if applicable
  if (user.is_admin) {
    statusBadge.textContent += " (Admin)";
  }
}

// Load user vehicles
async function loadUserVehicles() {
  try {
    const response = await fetch("/api/vehicles", {
      headers: window.authHeaders,
    });

    if (!response.ok) {
      throw new Error("Failed to load vehicles");
    }

    const data = await response.json();
    userVehicles = data.vehicles;

    // Update UI
    updateVehiclesUI(userVehicles);
    document.getElementById("vehicleCount").textContent = userVehicles.length;
  } catch (error) {
    console.error("Error loading vehicles:", error);
    document.getElementById("vehiclesList").innerHTML = `
            <div style="text-align: center; padding: 2rem; color: var(--danger-color);">
                <i class="fas fa-exclamation-triangle" style="font-size: 3rem; margin-bottom: 1rem;"></i>
                <p>Error cargando vehículos</p>
            </div>
        `;
  }
}

// Update vehicles UI
function updateVehiclesUI(vehicles) {
  const container = document.getElementById("vehiclesList");

  if (vehicles.length === 0) {
    container.innerHTML = `
            <div style="text-align: center; padding: 2rem; color: var(--gray-500);">
                <i class="fas fa-car" style="font-size: 3rem; margin-bottom: 1rem; opacity: 0.3;"></i>
                <p>No tienes vehículos registrados</p>
                <button class="btn btn-primary" onclick="showVehicleModal()" style="margin-top: 1rem;">
                    <i class="fas fa-plus"></i>
                    Agregar tu primer vehículo
                </button>
            </div>
        `;
    return;
  }

  container.innerHTML = vehicles
    .map(
      (vehicle) => `
        <div class="vehicle-item">
            <div class="vehicle-info">
                <div class="vehicle-plate">${vehicle.license_plate}</div>
                <div class="vehicle-details">
                    ${vehicle.make} ${vehicle.model} ${
        vehicle.year ? `(${vehicle.year})` : ""
      } - ${vehicle.color}
                    ${
                      vehicle.is_temporary
                        ? '<span class="badge badge-warning">Temporal</span>'
                        : ""
                    }
                    ${
                      vehicle.is_expired
                        ? '<span class="badge badge-danger">Expirado</span>'
                        : ""
                    }
                </div>
            </div>
            <div class="vehicle-actions">
                <button class="btn btn-secondary" onclick="editVehicle(${
                  vehicle.id
                })">
                    <i class="fas fa-edit"></i>
                    Editar
                </button>
                <button class="btn btn-danger" onclick="deleteVehicle(${
                  vehicle.id
                }, '${vehicle.license_plate}')">
                    <i class="fas fa-trash"></i>
                    Eliminar
                </button>
            </div>
        </div>
    `
    )
    .join("");
}

// Load user locations
async function loadUserLocations() {
  try {
    const response = await fetch("/api/locations", {
      headers: window.authHeaders,
    });

    if (!response.ok) {
      throw new Error("Failed to load locations");
    }

    const data = await response.json();
    userLocations = data.locations;

    // Update UI
    updateLocationsUI(userLocations);
    document.getElementById("locationCount").textContent = userLocations.length;
  } catch (error) {
    console.error("Error loading locations:", error);
    document.getElementById("locationsList").innerHTML = `
            <div style="text-align: center; padding: 2rem; color: var(--danger-color);">
                <i class="fas fa-exclamation-triangle" style="font-size: 3rem; margin-bottom: 1rem;"></i>
                <p>Error cargando ubicaciones</p>
            </div>
        `;
  }
}

// Update locations UI
function updateLocationsUI(locations) {
  const container = document.getElementById("locationsList");

  if (locations.length === 0) {
    container.innerHTML = `
            <div style="text-align: center; padding: 2rem; color: var(--gray-500);">
                <i class="fas fa-map-marker-alt" style="font-size: 3rem; margin-bottom: 1rem; opacity: 0.3;"></i>
                <p>No tienes ubicaciones asignadas</p>
                <p style="font-size: 0.875rem; margin-top: 0.5rem;">Contacta al administrador para obtener acceso a ubicaciones</p>
            </div>
        `;
    return;
  }

  container.innerHTML = locations
    .map(
      (location) => `
        <div class="location-item">
            <div class="location-info">
                <div style="font-weight: 600; color: var(--gray-900);">${
                  location.location_name
                }</div>
                <div style="color: var(--gray-600); font-size: 0.875rem; margin-top: 0.25rem;">
                    ${location.location_address || "Sin dirección especificada"}
                </div>
                <div style="margin-top: 0.5rem;">
                    <span class="badge ${
                      location.access_level === "admin"
                        ? "badge-danger"
                        : location.access_level === "resident"
                        ? "badge-success"
                        : "badge-warning"
                    }">
                        ${
                          location.access_level === "admin"
                            ? "Administrador"
                            : location.access_level === "resident"
                            ? "Residente"
                            : "Invitado"
                        }
                    </span>
                    ${
                      location.is_expired
                        ? '<span class="badge badge-danger">Expirado</span>'
                        : ""
                    }
                </div>
            </div>
        </div>
    `
    )
    .join("");
}

// Handle profile update
async function handleProfileUpdate(e) {
  e.preventDefault();

  const formData = new FormData(e.target);
  const data = {
    first_name: formData.get("first_name"),
    last_name: formData.get("last_name"),
    phone: formData.get("phone"),
  };

  setLoading("profile", true);

  try {
    const response = await fetch("/auth/me", {
      method: "PUT",
      headers: window.authHeaders,
      body: JSON.stringify(data),
    });

    const result = await response.json();

    if (response.ok && result.success) {
      showAlert("Perfil actualizado exitosamente", "success");
      currentUser = result.user;
      updateProfileUI(currentUser);
    } else {
      showAlert(result.error || "Error actualizando perfil", "error");
    }
  } catch (error) {
    console.error("Error updating profile:", error);
    showAlert("Error de conexión", "error");
  } finally {
    setLoading("profile", false);
  }
}

// Handle password change
async function handlePasswordChange(e) {
  e.preventDefault();

  const formData = new FormData(e.target);
  const data = {
    current_password: formData.get("current_password"),
    new_password: formData.get("new_password"),
    confirm_password: formData.get("confirm_password"),
  };

  if (data.new_password !== data.confirm_password) {
    showAlert("Las contraseñas nuevas no coinciden", "error");
    return;
  }

  setLoading("password", true);

  try {
    const response = await fetch("/auth/change-password", {
      method: "POST",
      headers: window.authHeaders,
      body: JSON.stringify(data),
    });

    const result = await response.json();

    if (response.ok && result.success) {
      showAlert("Contraseña actualizada exitosamente", "success");
      closePasswordModal();
      document.getElementById("passwordForm").reset();
    } else {
      showAlert(result.error || "Error cambiando contraseña", "error");
    }
  } catch (error) {
    console.error("Error changing password:", error);
    showAlert("Error de conexión", "error");
  } finally {
    setLoading("password", false);
  }
}

// Handle vehicle form submission
async function handleVehicleSubmit(e) {
  e.preventDefault();

  const formData = new FormData(e.target);
  const vehicleId = formData.get("vehicleId");
  const data = {
    plate: formData.get("license_plate"),
    make: formData.get("make"),
    model: formData.get("model"),
    year: formData.get("year") ? parseInt(formData.get("year")) : null,
    color: formData.get("color"),
    vehicle_type: formData.get("vehicle_type"),
  };

  setLoading("vehicle", true);

  try {
    let response;
    if (vehicleId) {
      // Update existing vehicle
      response = await fetch(`/api/vehicles/${vehicleId}`, {
        method: "PUT",
        headers: window.authHeaders,
        body: JSON.stringify(data),
      });
    } else {
      // Create new vehicle
      response = await fetch("/api/plates", {
        method: "POST",
        headers: window.authHeaders,
        body: JSON.stringify(data),
      });
    }

    const result = await response.json();

    if (response.ok && result.success) {
      showAlert(
        vehicleId
          ? "Vehículo actualizado exitosamente"
          : "Vehículo agregado exitosamente",
        "success"
      );
      closeVehicleModal();
      loadUserVehicles(); // Reload vehicles
    } else {
      showAlert(result.error || "Error guardando vehículo", "error");
    }
  } catch (error) {
    console.error("Error saving vehicle:", error);
    showAlert("Error de conexión", "error");
  } finally {
    setLoading("vehicle", false);
  }
}

// Show vehicle modal
function showVehicleModal(vehicle = null) {
  const modal = document.getElementById("vehicleModal");
  const form = document.getElementById("vehicleForm");
  const title = document.getElementById("vehicleModalTitle");

  if (vehicle) {
    // Edit mode
    title.textContent = "Editar Vehículo";
    document.getElementById("vehicleId").value = vehicle.id;
    document.getElementById("license_plate").value = vehicle.license_plate;
    document.getElementById("make").value = vehicle.make || "";
    document.getElementById("model").value = vehicle.model || "";
    document.getElementById("year").value = vehicle.year || "";
    document.getElementById("color").value = vehicle.color || "";
    document.getElementById("vehicle_type").value =
      vehicle.vehicle_type || "car";
  } else {
    // Add mode
    title.textContent = "Agregar Vehículo";
    form.reset();
    document.getElementById("vehicleId").value = "";
  }

  modal.classList.add("show");
}

// Close vehicle modal
function closeVehicleModal() {
  document.getElementById("vehicleModal").classList.remove("show");
  document.getElementById("vehicleForm").reset();
}

// Edit vehicle
function editVehicle(vehicleId) {
  const vehicle = userVehicles.find((v) => v.id === vehicleId);
  if (vehicle) {
    showVehicleModal(vehicle);
  }
}

// Delete vehicle
async function deleteVehicle(vehicleId, licensePlate) {
  if (
    !confirm(
      `¿Estás seguro de que quieres eliminar el vehículo ${licensePlate}?`
    )
  ) {
    return;
  }

  try {
    const response = await fetch(`/api/vehicles/${vehicleId}`, {
      method: "DELETE",
      headers: window.authHeaders,
    });

    const result = await response.json();

    if (response.ok && result.success) {
      showAlert("Vehículo eliminado exitosamente", "success");
      loadUserVehicles(); // Reload vehicles
    } else {
      showAlert(result.error || "Error eliminando vehículo", "error");
    }
  } catch (error) {
    console.error("Error deleting vehicle:", error);
    showAlert("Error de conexión", "error");
  }
}

// Show password modal
function showPasswordModal() {
  document.getElementById("passwordModal").classList.add("show");
}

// Close password modal
function closePasswordModal() {
  document.getElementById("passwordModal").classList.remove("show");
  document.getElementById("passwordForm").reset();
}

// Utility functions
function showAlert(message, type = "error") {
  const alertContainer = document.getElementById("alert-container");
  const alertClass = type === "error" ? "alert-error" : "alert-success";

  alertContainer.innerHTML = `
        <div class="alert ${alertClass}">
            <i class="fas fa-${
              type === "error" ? "exclamation-circle" : "check-circle"
            }"></i>
            ${message}
        </div>
    `;

  // Auto-hide after 5 seconds
  setTimeout(() => {
    alertContainer.innerHTML = "";
  }, 5000);
}

function setLoading(type, loading) {
  const spinner = document.getElementById(`${type}Spinner`);
  const text = document.getElementById(`${type}BtnText`);
  const btn = spinner.closest("button");

  btn.disabled = loading;

  if (loading) {
    spinner.classList.add("show");
    if (type === "profile") text.textContent = "Guardando...";
    else if (type === "vehicle") text.textContent = "Guardando...";
    else if (type === "password") text.textContent = "Cambiando...";
  } else {
    spinner.classList.remove("show");
    if (type === "profile") text.textContent = "Guardar Cambios";
    else if (type === "vehicle") text.textContent = "Guardar Vehículo";
    else if (type === "password") text.textContent = "Cambiar Contraseña";
  }
}

function logout() {
  if (confirm("¿Estás seguro de que quieres cerrar sesión?")) {
    localStorage.removeItem("authToken");
    window.location.href = "/login";
  }
}

// Mobile menu toggle (placeholder for future mobile menu implementation)
function toggleMobileMenu() {
  // For now, just show the user menu options in an alert
  // In a full implementation, this would show/hide a mobile menu
  const options = "Dashboard\nMi Perfil\nCerrar Sesión";
  alert("Menú:\n" + options);
}
