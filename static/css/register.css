/* VecinoSeguro Register Page Specific Styles */

.register-container {
  background: white;
  border-radius: 16px;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04);
  padding: 40px;
  width: 100%;
  max-width: 500px;
}

.register-container .logo {
  margin-bottom: 24px;
}

.register-container .logo h1 {
  font-size: 24px;
}

.register-container .logo p {
  font-size: 13px;
}

/* Form specific styles for register */
.register-form .form-row {
  display: flex;
  gap: 16px;
  margin-bottom: 0;
}

.register-form .form-row .form-group {
  flex: 1;
  margin-bottom: 24px;
}

/* Password strength indicator */
.password-strength {
  margin-top: 8px;
  font-size: 12px;
}

.password-strength .strength-bar {
  height: 4px;
  background: var(--gray-200);
  border-radius: 2px;
  margin-bottom: 4px;
  overflow: hidden;
}

.password-strength .strength-fill {
  height: 100%;
  transition: all 0.3s;
  border-radius: 2px;
}

.password-strength.weak .strength-fill {
  width: 33%;
  background: var(--danger-color);
}

.password-strength.medium .strength-fill {
  width: 66%;
  background: var(--warning-color);
}

.password-strength.strong .strength-fill {
  width: 100%;
  background: var(--success-color);
}

.password-strength .strength-text {
  color: var(--gray-600);
}

.password-strength.weak .strength-text {
  color: var(--danger-color);
}

.password-strength.medium .strength-text {
  color: var(--warning-color);
}

.password-strength.strong .strength-text {
  color: var(--success-color);
}

/* Terms and conditions */
.terms-group {
  margin-bottom: 24px;
}

.terms-group .checkbox-group {
  margin-bottom: 0;
  align-items: flex-start;
}

.terms-group .checkbox-group input[type="checkbox"] {
  margin-top: 2px;
}

.terms-group .checkbox-group label {
  line-height: 1.4;
}

.terms-group .checkbox-group a {
  color: var(--primary-color);
  text-decoration: none;
}

.terms-group .checkbox-group a:hover {
  text-decoration: underline;
}

/* Login link */
.login-link {
  text-align: center;
  margin-top: 24px;
}

.login-link a {
  color: var(--primary-color);
  text-decoration: none;
  font-weight: 500;
}

.login-link a:hover {
  text-decoration: underline;
}

/* Responsive adjustments for register */
@media (max-width: 768px) {
  .register-container {
    max-width: 400px;
  }
  
  .register-form .form-row {
    flex-direction: column;
    gap: 0;
  }
}

@media (max-width: 480px) {
  .register-container {
    padding: 24px;
  }
}
