from flask import Blueprint, request, jsonify, current_app
from flask_login import current_user
from datetime import datetime, timedelta
from sqlalchemy import or_
from functools import wraps

from src.models.models import db, User, UserLocation, Vehicle, AccessLog
from src.api.auth import jwt_required, admin_required, resource_owner_or_admin_required
from src.api.validation import InputValidator, ValidationError, handle_validation_error

# Create API blueprint
api_bp = Blueprint('api', __name__, url_prefix='/api')

# Vehicle Management Routes
@api_bp.route('/vehicles', methods=['GET'])
@jwt_required
def get_user_vehicles():
    """Get vehicles for current user"""
    try:
        user = request.current_user
        vehicles = [vehicle.to_dict() for vehicle in user.vehicles if vehicle.is_active]
        
        return jsonify({
            'success': True,
            'vehicles': vehicles
        })
        
    except Exception as e:
        current_app.logger.error(f"Error getting vehicles: {str(e)}")
        return jsonify({'error': 'Error interno del servidor'}), 500

@api_bp.route('/vehicles/<int:vehicle_id>', methods=['PUT'])
@jwt_required
@handle_validation_error
def update_vehicle(vehicle_id):
    """Update vehicle information with proper authorization and validation"""
    user = request.current_user

    # Check if vehicle exists and user has permission
    if user.is_admin:
        vehicle = Vehicle.query.get(vehicle_id)
    else:
        vehicle = Vehicle.query.filter_by(id=vehicle_id, user_id=user.id).first()

    if not vehicle:
        return jsonify({'error': 'Vehículo no encontrado o sin permisos'}), 404

    data = request.get_json()
    if not data:
        return jsonify({'error': 'Datos JSON requeridos'}), 400

    # Validate input data
    update_data = {}

    # Validate and sanitize fields
    if 'make' in data:
        update_data['make'] = InputValidator.sanitize_string(data['make'], 50) or vehicle.make

    if 'model' in data:
        update_data['model'] = InputValidator.sanitize_string(data['model'], 50) or vehicle.model

    if 'color' in data:
        update_data['color'] = InputValidator.sanitize_string(data['color'], 30) or vehicle.color

    if 'year' in data:
        year = data['year']
        if year:
            try:
                year = int(year)
                current_year = datetime.now().year
                if year < 1900 or year > current_year + 2:
                    raise ValidationError(f'Año inválido (debe estar entre 1900 y {current_year + 2})')
                update_data['year'] = year
            except (ValueError, TypeError):
                raise ValidationError('Año debe ser un número válido')

    if 'vehicle_type' in data:
        valid_types = ['car', 'motorcycle', 'truck', 'van', 'suv', 'bus']
        vehicle_type = data['vehicle_type'].lower().strip()
        if vehicle_type in valid_types:
            update_data['vehicle_type'] = vehicle_type

    # Only allow admin or temporary vehicle owner to change temporary status
    if 'is_temporary' in data and (user.is_admin or vehicle.is_temporary):
        is_temporary = bool(data['is_temporary'])
        update_data['is_temporary'] = is_temporary

        if is_temporary and not vehicle.expires_at:
            update_data['expires_at'] = datetime.utcnow() + timedelta(days=30)
        elif not is_temporary:
            update_data['expires_at'] = None

    # Apply updates
    for field, value in update_data.items():
        setattr(vehicle, field, value)

    db.session.commit()

    current_app.logger.info(f"Vehicle {vehicle.license_plate} updated by {user.email}")

    return jsonify({
        'success': True,
        'vehicle': vehicle.to_dict(),
        'message': 'Vehículo actualizado exitosamente'
    })

@api_bp.route('/vehicles/<int:vehicle_id>', methods=['DELETE'])
@jwt_required
def delete_vehicle(vehicle_id):
    """Delete/deactivate vehicle"""
    try:
        user = request.current_user
        vehicle = Vehicle.query.filter_by(id=vehicle_id, user_id=user.id).first()
        
        if not vehicle:
            return jsonify({'error': 'Vehículo no encontrado'}), 404
        
        # Soft delete - just deactivate
        vehicle.is_active = False
        db.session.commit()
        
        current_app.logger.info(f"Vehicle deactivated: {vehicle.license_plate} by {user.email}")
        
        return jsonify({
            'success': True,
            'message': 'Vehículo eliminado exitosamente'
        })
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error deleting vehicle: {str(e)}")
        return jsonify({'error': 'Error interno del servidor'}), 500

# Location Management Routes
@api_bp.route('/locations', methods=['GET'])
@jwt_required
def get_user_locations():
    """Get locations for current user"""
    try:
        user = request.current_user
        locations = [location.to_dict() for location in user.locations if location.is_active]
        
        return jsonify({
            'success': True,
            'locations': locations
        })
        
    except Exception as e:
        current_app.logger.error(f"Error getting locations: {str(e)}")
        return jsonify({'error': 'Error interno del servidor'}), 500

@api_bp.route('/locations', methods=['POST'])
@jwt_required
def add_user_location():
    """Add location access for current user (admin only)"""
    try:
        user = request.current_user
        
        if not user.is_admin:
            return jsonify({'error': 'Solo administradores pueden agregar ubicaciones'}), 403
        
        data = request.get_json()
        location_name = data.get('location_name', '').strip()
        location_address = data.get('location_address', '').strip()
        access_level = data.get('access_level', 'resident').strip()
        target_user_id = data.get('user_id')
        
        if not location_name:
            return jsonify({'error': 'Nombre de ubicación requerido'}), 400
        
        if not target_user_id:
            target_user_id = user.id
        
        # Validate target user exists
        target_user = User.query.get(target_user_id)
        if not target_user:
            return jsonify({'error': 'Usuario no encontrado'}), 404
        
        # Check if location already exists for user
        existing_location = UserLocation.query.filter_by(
            user_id=target_user_id,
            location_name=location_name
        ).first()
        
        if existing_location:
            return jsonify({'error': 'El usuario ya tiene acceso a esta ubicación'}), 400
        
        # Create new location
        location = UserLocation(
            user_id=target_user_id,
            location_name=location_name,
            location_address=location_address,
            access_level=access_level,
            is_active=True
        )
        
        # Set expiration for guest access
        if access_level == 'guest':
            expires_days = data.get('expires_days', 30)
            location.expires_at = datetime.utcnow() + timedelta(days=expires_days)
        
        db.session.add(location)
        db.session.commit()
        
        return jsonify({
            'success': True,
            'location': location.to_dict(),
            'message': 'Ubicación agregada exitosamente'
        })
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error adding location: {str(e)}")
        return jsonify({'error': 'Error interno del servidor'}), 500

@api_bp.route('/locations/<int:location_id>', methods=['DELETE'])
@jwt_required
def remove_user_location(location_id):
    """Remove location access"""
    try:
        user = request.current_user
        
        # Users can only remove their own locations, admins can remove any
        if user.is_admin:
            location = UserLocation.query.get(location_id)
        else:
            location = UserLocation.query.filter_by(id=location_id, user_id=user.id).first()
        
        if not location:
            return jsonify({'error': 'Ubicación no encontrada'}), 404
        
        # Soft delete
        location.is_active = False
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': 'Acceso a ubicación removido exitosamente'
        })
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error removing location: {str(e)}")
        return jsonify({'error': 'Error interno del servidor'}), 500

# Access Log Routes
@api_bp.route('/access-logs', methods=['GET'])
@jwt_required
def get_access_logs():
    """Get access logs for current user"""
    try:
        user = request.current_user
        hours = request.args.get('hours', 24, type=int)
        limit = request.args.get('limit', 100, type=int)
        
        # Calculate time filter
        since = datetime.utcnow() - timedelta(hours=hours)
        
        # Build query
        if user.is_admin:
            # Admin can see all logs
            query = AccessLog.query
        else:
            # Regular users see only their logs
            query = AccessLog.query.filter_by(user_id=user.id)
        
        logs = query.filter(AccessLog.timestamp >= since)\
                   .order_by(AccessLog.timestamp.desc())\
                   .limit(limit)\
                   .all()
        
        return jsonify({
            'success': True,
            'logs': [log.to_dict() for log in logs],
            'total': len(logs)
        })
        
    except Exception as e:
        current_app.logger.error(f"Error getting access logs: {str(e)}")
        return jsonify({'error': 'Error interno del servidor'}), 500

# Admin Routes
@api_bp.route('/admin/users', methods=['GET'])
@jwt_required
@admin_required
def get_all_users():
    """Get all users (admin only)"""
    try:
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)
        search = request.args.get('search', '').strip()
        
        query = User.query
        
        if search:
            query = query.filter(
                or_(
                    User.email.contains(search),
                    User.first_name.contains(search),
                    User.last_name.contains(search)
                )
            )
        
        pagination = query.paginate(
            page=page, 
            per_page=per_page, 
            error_out=False
        )
        
        return jsonify({
            'success': True,
            'users': [user.to_dict() for user in pagination.items],
            'pagination': {
                'page': page,
                'per_page': per_page,
                'total': pagination.total,
                'pages': pagination.pages,
                'has_next': pagination.has_next,
                'has_prev': pagination.has_prev
            }
        })
        
    except Exception as e:
        current_app.logger.error(f"Error getting users: {str(e)}")
        return jsonify({'error': 'Error interno del servidor'}), 500

@api_bp.route('/admin/users/<int:user_id>/toggle-status', methods=['POST'])
@jwt_required
@admin_required
def toggle_user_status(user_id):
    """Toggle user active status (admin only)"""
    try:
        user = User.query.get(user_id)
        if not user:
            return jsonify({'error': 'Usuario no encontrado'}), 404
        
        # Don't allow deactivating the last admin
        if user.is_admin and user.is_active:
            active_admins = User.query.filter_by(is_admin=True, is_active=True).count()
            if active_admins <= 1:
                return jsonify({'error': 'No se puede desactivar el último administrador'}), 400
        
        user.is_active = not user.is_active
        user.updated_at = datetime.utcnow()
        db.session.commit()
        
        status = 'activado' if user.is_active else 'desactivado'
        return jsonify({
            'success': True,
            'message': f'Usuario {status} exitosamente',
            'user': user.to_dict()
        })
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error toggling user status: {str(e)}")
        return jsonify({'error': 'Error interno del servidor'}), 500

@api_bp.route('/admin/stats', methods=['GET'])
@jwt_required
@admin_required
def get_admin_stats():
    """Get system statistics (admin only)"""
    try:
        # Get basic counts
        total_users = User.query.count()
        active_users = User.query.filter_by(is_active=True).count()
        total_vehicles = Vehicle.query.filter_by(is_active=True).count()
        total_locations = UserLocation.query.filter_by(is_active=True).count()
        
        # Get recent access logs
        hours = request.args.get('hours', 24, type=int)
        since = datetime.utcnow() - timedelta(hours=hours)
        
        recent_accesses = AccessLog.query.filter(AccessLog.timestamp >= since).count()
        successful_accesses = AccessLog.query.filter(
            AccessLog.timestamp >= since,
            AccessLog.access_granted == True
        ).count()
        
        return jsonify({
            'success': True,
            'stats': {
                'users': {
                    'total': total_users,
                    'active': active_users,
                    'inactive': total_users - active_users
                },
                'vehicles': {
                    'total': total_vehicles
                },
                'locations': {
                    'total': total_locations
                },
                'access_logs': {
                    'recent_total': recent_accesses,
                    'recent_successful': successful_accesses,
                    'recent_failed': recent_accesses - successful_accesses,
                    'hours': hours
                }
            }
        })
        
    except Exception as e:
        current_app.logger.error(f"Error getting admin stats: {str(e)}")
        return jsonify({'error': 'Error interno del servidor'}), 500

def init_api_routes(app):
    """Initialize API routes with Flask app"""
    app.register_blueprint(api_bp)
